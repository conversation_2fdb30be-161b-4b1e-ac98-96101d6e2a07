#include "../../../lvgl.h"

#if LV_USE_DEMO_BENCHMARK

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_IMG_COGWHEEL_INDEXED16
#define LV_ATTRIBUTE_IMG_IMG_COGWHEEL_INDEXED16
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG_IMG_COGWHEEL_INDEXED16 uint8_t img_benchmark_cogwheel_indexed16_map[] = {
  0x00, 0x00, 0x00, 0x00,   /*Color of index 0*/
  0x6c, 0x5e, 0x4a, 0xff,   /*Color of index 1*/
  0x72, 0x64, 0x50, 0xff,   /*Color of index 2*/
  0x6c, 0x65, 0x5f, 0xff,   /*Color of index 3*/
  0x7b, 0x6b, 0x58, 0xff,   /*Color of index 4*/
  0x82, 0x70, 0x60, 0xff,   /*Color of index 5*/
  0x81, 0x75, 0x68, 0xff,   /*Color of index 6*/
  0x7d, 0x74, 0x72, 0xff,   /*Color of index 7*/
  0x8e, 0x78, 0x67, 0xff,   /*Color of index 8*/
  0x98, 0x81, 0x6e, 0xff,   /*Color of index 9*/
  0x8d, 0x81, 0x73, 0xff,   /*Color of index 10*/
  0x89, 0x81, 0x7d, 0xff,   /*Color of index 11*/
  0xa7, 0x8e, 0x7c, 0xff,   /*Color of index 12*/
  0xa3, 0x96, 0x8b, 0xff,   /*Color of index 13*/
  0x9d, 0x96, 0x92, 0xff,   /*Color of index 14*/
  0xb3, 0x9e, 0x8e, 0xff,   /*Color of index 15*/

  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x88, 0x88, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x89, 0x88, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0x89, 0x88, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x99, 0x99, 0x89, 0x00, 0x00, 0x00, 0x00, 0x05, 0x55, 0x9d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x99, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0x99, 0x99, 0x89, 0xd0, 0x00, 0x00, 0x00, 0xc5, 0x45, 0x56, 0x9d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x99, 0xcc, 0x9c, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x98, 0x99, 0x89, 0x88, 0xa0, 0x00, 0x00, 0x0d, 0x58, 0x85, 0x55, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xc9, 0xc9, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x99, 0x89, 0x89, 0x98, 0x90, 0x00, 0x00, 0x0a, 0x58, 0x68, 0x58, 0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0xcc, 0x9c, 0xc9, 0xc0, 0x00, 0x00, 0x00, 0x0c, 0x99, 0x99, 0x99, 0x89, 0x70, 0x00, 0x00, 0x09, 0x85, 0x84, 0x85, 0x58, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xcc, 0xc9, 0xc9, 0x9f, 0x00, 0x00, 0x00, 0x09, 0x99, 0x99, 0x99, 0x88, 0x9c, 0x00, 0x00, 0xa5, 0x88, 0x85, 0x58, 0x48, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x9c, 0xcc, 0xc9, 0xcc, 0xf0, 0x00, 0x0d, 0x98, 0x99, 0x98, 0x99, 0x99, 0x88, 0x90, 0x0c, 0x58, 0x86, 0x86, 0x55, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xc9, 0xc9, 0xcc, 0x99, 0xc9, 0xc9, 0x89, 0x99, 0x99, 0xa9, 0x98, 0x89, 0x89, 0x85, 0x68, 0x89, 0x88, 0x88, 0x68, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0xc9, 0xcc, 0x99, 0x99, 0x99, 0x99, 0x89, 0x98, 0x99, 0x99, 0x88, 0x88, 0x88, 0x58, 0x88, 0x85, 0x84, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0xc9, 0xcc, 0x9c, 0xc9, 0xc9, 0xc9, 0x99, 0x99, 0x99, 0x98, 0x9a, 0x88, 0x89, 0x88, 0x68, 0x58, 0x86, 0x85, 0x66, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x5a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0x9c, 0x99, 0x9c, 0x99, 0x9c, 0x99, 0x99, 0x99, 0x9a, 0x89, 0x89, 0x88, 0x69, 0x88, 0x95, 0x88, 0x58, 0x85, 0x00, 0x00, 0x00, 0x0d, 0x54, 0x41, 0x6d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcf, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0x9c, 0xc9, 0xcc, 0x9c, 0xc9, 0x99, 0xc9, 0x99, 0x98, 0x99, 0x89, 0x89, 0x89, 0x88, 0x68, 0x85, 0x88, 0x45, 0x55, 0x9d, 0x00, 0x00, 0xa6, 0x44, 0x44, 0x44, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0xc0, 0x00, 0x00, 0x0c, 0xcc, 0xcc, 0xc9, 0xcc, 0x99, 0xc9, 0x9c, 0x99, 0x88, 0x89, 0x99, 0x99, 0x98, 0x68, 0x88, 0x89, 0x88, 0x68, 0x58, 0x86, 0x55, 0x55, 0x90, 0x0a, 0x84, 0x44, 0x54, 0x42, 0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcf, 0xcc, 0xcc, 0xf0, 0x00, 0xfc, 0xcc, 0x9c, 0xcc, 0x9c, 0xcc, 0x99, 0x85, 0x53, 0x45, 0x89, 0x99, 0x99, 0x88, 0x24, 0x44, 0x45, 0x88, 0x98, 0x88, 0x85, 0x55, 0x54, 0x59, 0xa4, 0x44, 0x84, 0x54, 0x54, 0x4c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcf, 0xcc, 0xcc, 0xfc, 0xcc, 0xcc, 0x9c, 0xcc, 0xcc, 0x9c, 0xcc, 0x89, 0x85, 0x42, 0x11, 0x12, 0x89, 0x99, 0x89, 0xa4, 0x11, 0x11, 0x24, 0x45, 0x56, 0x55, 0x86, 0x65, 0x55, 0x44, 0x44, 0x44, 0x45, 0x44, 0x42, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xfc, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc9, 0x98, 0x52, 0x12, 0x21, 0x11, 0x13, 0x89, 0x99, 0x99, 0x95, 0x11, 0x11, 0x11, 0x11, 0x45, 0x84, 0x88, 0x55, 0x55, 0x54, 0x48, 0x45, 0x45, 0x44, 0x44, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xcc, 0xcc, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc8, 0x54, 0x21, 0x11, 0x12, 0x22, 0x12, 0x89, 0x99, 0x89, 0x85, 0x21, 0x11, 0x11, 0x22, 0x22, 0x44, 0x45, 0x55, 0x55, 0x45, 0x54, 0x54, 0x44, 0x54, 0x44, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0xcf, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x99, 0x52, 0x11, 0x11, 0x22, 0x11, 0x11, 0x47, 0x89, 0x99, 0x99, 0x84, 0x11, 0x42, 0x11, 0x11, 0x22, 0x24, 0x45, 0x55, 0x58, 0x44, 0x54, 0x54, 0x54, 0x54, 0x45, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc9, 0x94, 0x11, 0x21, 0x21, 0x11, 0x36, 0xbe, 0x00, 0xc9, 0x99, 0x99, 0x81, 0x30, 0x00, 0x0a, 0x52, 0x11, 0x44, 0x44, 0x55, 0x55, 0x55, 0x48, 0x45, 0x44, 0x42, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc5, 0x22, 0x11, 0x11, 0x21, 0x36, 0xb0, 0x00, 0x00, 0xf9, 0x89, 0x99, 0x57, 0xe0, 0x00, 0x00, 0x0d, 0x54, 0x22, 0x44, 0x44, 0x55, 0x54, 0x44, 0x45, 0x45, 0x41, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0xcc, 0xcc, 0xc9, 0x52, 0x11, 0x22, 0x11, 0x3b, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x99, 0x89, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 0x24, 0x55, 0x45, 0x45, 0x85, 0x54, 0x44, 0x54, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0xcc, 0xcc, 0xc5, 0x11, 0x12, 0x11, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x99, 0x89, 0x5a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x44, 0x48, 0x44, 0x44, 0x44, 0x45, 0x44, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0xcc, 0xcc, 0xc9, 0x41, 0x11, 0x21, 0x23, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x89, 0x99, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x44, 0x55, 0x55, 0x84, 0x85, 0x44, 0x42, 0x6d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xcc, 0xcc, 0xfc, 0xc4, 0x11, 0x32, 0x11, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x89, 0x99, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0x45, 0x55, 0x44, 0x44, 0x45, 0x84, 0x25, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0xcc, 0xc9, 0x52, 0x12, 0x11, 0x3b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x98, 0x98, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x58, 0x88, 0x54, 0x44, 0x44, 0x42, 0xd0, 0x00, 0xda, 0x42, 0x2a, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0xcc, 0xcc, 0xc5, 0x11, 0x12, 0x11, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x99, 0x99, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x55, 0x85, 0x54, 0x44, 0x45, 0x44, 0x26, 0x64, 0x21, 0x42, 0x24, 0xd0, 0x00, 0x00,
  0x00, 0x00, 0x0c, 0xcc, 0xcc, 0xcf, 0x00, 0xfc, 0xcc, 0xcc, 0xcc, 0x81, 0x12, 0x21, 0x3b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x89, 0x88, 0x8b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x89, 0x85, 0x54, 0x44, 0x44, 0x42, 0x11, 0x14, 0x24, 0x21, 0x60, 0x00, 0x00,
  0x00, 0x00, 0x0c, 0x9c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc8, 0x21, 0x32, 0x11, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x89, 0x98, 0x8a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x88, 0x98, 0x54, 0x44, 0x42, 0x44, 0x44, 0x42, 0x24, 0x21, 0x40, 0x00, 0x00,
  0x00, 0x00, 0xcc, 0xcc, 0xc9, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x94, 0x12, 0x12, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x98, 0x88, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x88, 0x98, 0x84, 0x44, 0x25, 0x44, 0x41, 0x52, 0x42, 0x41, 0x26, 0x00, 0x00,
  0x00, 0x00, 0x99, 0xc9, 0xcc, 0xc9, 0xc9, 0xc9, 0xcc, 0xcc, 0x52, 0x12, 0x21, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x9a, 0x98, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x99, 0x95, 0x44, 0x44, 0x22, 0x42, 0x41, 0x22, 0x41, 0x24, 0x00, 0x00,
  0x00, 0x00, 0x99, 0xcc, 0x9c, 0x9c, 0xcc, 0xcc, 0xc9, 0xc9, 0x41, 0x22, 0x13, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x88, 0x88, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x89, 0x98, 0x54, 0x24, 0x45, 0x24, 0x24, 0x42, 0x22, 0x2b, 0x00, 0x00,
  0x00, 0x00, 0xc9, 0x9c, 0xcc, 0x9c, 0x9c, 0x9c, 0xcc, 0x95, 0x21, 0x21, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x99, 0x98, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x99, 0x99, 0x54, 0x44, 0x22, 0x41, 0x51, 0x42, 0x14, 0xb0, 0x00, 0x00,
  0x00, 0x00, 0x0c, 0x99, 0x9c, 0xc9, 0xc9, 0xc9, 0x9c, 0x94, 0x12, 0x22, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x88, 0x88, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x9c, 0x85, 0x42, 0x52, 0x42, 0x42, 0x41, 0x3e, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xfc, 0x99, 0xc9, 0xc9, 0xcc, 0xc9, 0x51, 0x12, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x88, 0x89, 0x88, 0x58, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x9c, 0x95, 0x24, 0x14, 0x15, 0x11, 0x15, 0xe0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xcc, 0x9c, 0x9c, 0x99, 0xc9, 0x52, 0x22, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x98, 0x98, 0x98, 0x88, 0x89, 0x59, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xc9, 0xc8, 0x52, 0x52, 0x42, 0x51, 0x3e, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0x9c, 0xc9, 0xc5, 0x22, 0x21, 0x2b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x88, 0x96, 0x98, 0x98, 0x68, 0x85, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9c, 0xc9, 0x52, 0x25, 0x14, 0x22, 0xe0, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0xc9, 0x9c, 0x95, 0x22, 0x24, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x58, 0x99, 0x89, 0x8a, 0x88, 0x88, 0x68, 0x84, 0x5d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xc9, 0x84, 0x42, 0x42, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0x9c, 0x9c, 0x94, 0x12, 0x21, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x54, 0x89, 0x88, 0x88, 0x55, 0x58, 0x88, 0x84, 0x54, 0x45, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0x84, 0x15, 0x14, 0x41, 0xe0, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0xc9, 0x99, 0x84, 0x12, 0x24, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x24, 0xee, 0xc6, 0x85, 0x48, 0x68, 0x58, 0x56, 0xaa, 0x44, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0x94, 0x42, 0x42, 0x21, 0xa0, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0x99, 0xc9, 0x52, 0x12, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x4a, 0x00, 0x0a, 0xad, 0xed, 0xd0, 0xd9, 0x9e, 0x00, 0xc6, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcf, 0xc5, 0x15, 0x15, 0x21, 0x60, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0f, 0x89, 0xc9, 0x99, 0x52, 0x21, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x98, 0x5d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x55, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xc8, 0x51, 0x22, 0x42, 0x4b, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0c, 0x99, 0x99, 0x99, 0x44, 0x24, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x89, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x54, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xc8, 0x51, 0x42, 0x51, 0x44, 0x22, 0x44, 0x4a, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xd9, 0x99, 0xc9, 0x99, 0x54, 0x54, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x98, 0x88, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x55, 0x55, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0xc8, 0x52, 0x42, 0x14, 0x21, 0x11, 0x11, 0x22, 0x60,
  0x00, 0x0f, 0xfc, 0xc9, 0x99, 0x99, 0x99, 0x99, 0x88, 0x88, 0x89, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x98, 0x98, 0x94, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x45, 0x84, 0x48, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xc5, 0x58, 0x55, 0x22, 0x42, 0x22, 0x22, 0x22, 0x21, 0x11, 0x20,
  0x09, 0x88, 0x98, 0x98, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x9c, 0xcc, 0xcc, 0xc9, 0x99, 0x88, 0x89, 0x89, 0x55, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x85, 0x45, 0x54, 0x45, 0x45, 0x44, 0x44, 0x44, 0x44, 0x44, 0x22, 0x42, 0x24, 0x14, 0x22, 0x42, 0x22, 0x21, 0x32, 0x11, 0x20,
  0x09, 0x88, 0x99, 0x99, 0x89, 0x99, 0x99, 0x99, 0x99, 0x99, 0x98, 0x99, 0x88, 0x88, 0x88, 0x98, 0x88, 0x98, 0x69, 0x88, 0x84, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x94, 0x55, 0x55, 0x45, 0x44, 0x44, 0x44, 0x42, 0x22, 0x22, 0x22, 0x22, 0x24, 0x15, 0x14, 0x12, 0x22, 0x21, 0x13, 0x11, 0x10,
  0x09, 0x89, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0xa9, 0xa9, 0x99, 0x89, 0x88, 0x98, 0x98, 0x95, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x94, 0x55, 0x54, 0x44, 0x45, 0x45, 0x44, 0x44, 0x44, 0x44, 0x44, 0x52, 0x41, 0x52, 0x42, 0x42, 0x21, 0x22, 0x11, 0x11, 0x2b,
  0x0a, 0x88, 0x98, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x89, 0x99, 0x98, 0x98, 0x89, 0x89, 0x98, 0x88, 0x88, 0x85, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x94, 0x55, 0x55, 0x55, 0x54, 0x44, 0x44, 0x44, 0x54, 0x44, 0x24, 0x14, 0x24, 0x14, 0x12, 0x22, 0x21, 0x12, 0x11, 0x11, 0x1b,
  0x09, 0x8a, 0x99, 0x98, 0x98, 0x99, 0x99, 0x98, 0x99, 0x99, 0x99, 0x99, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0xa8, 0x65, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x85, 0x55, 0x54, 0x45, 0x44, 0x42, 0x44, 0x24, 0x11, 0x11, 0x15, 0x24, 0x11, 0x51, 0x42, 0x22, 0x22, 0x21, 0x11, 0x11, 0x30,
  0x09, 0x88, 0x88, 0x89, 0x98, 0x98, 0x98, 0x99, 0x98, 0x98, 0x99, 0x88, 0x99, 0x9a, 0x98, 0x89, 0x88, 0x89, 0x88, 0x88, 0x85, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x54, 0x44, 0x44, 0x45, 0x44, 0x44, 0x44, 0x45, 0x54, 0x54, 0x41, 0x42, 0x51, 0x41, 0x22, 0x12, 0x22, 0x11, 0x13, 0x34, 0xb0,
  0x09, 0x88, 0x89, 0x88, 0x89, 0x89, 0x89, 0x98, 0x55, 0x55, 0x54, 0x7d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0xa5, 0x98, 0x85, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x45, 0x54, 0x84, 0x45, 0x9d, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x85, 0x54, 0x55, 0x24, 0x22, 0x21, 0x31, 0x33, 0xe0, 0x00, 0x00,
  0x00, 0x0f, 0xdc, 0xca, 0x98, 0x89, 0x88, 0x98, 0x41, 0x11, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x88, 0x85, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x45, 0x45, 0x45, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x85, 0x84, 0x41, 0x24, 0x22, 0x11, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xd9, 0x98, 0x98, 0x95, 0x41, 0x11, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x58, 0x58, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0x54, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x88, 0x85, 0x42, 0x12, 0x11, 0x13, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x09, 0x68, 0x89, 0x98, 0x41, 0x11, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x85, 0x4a, 0x00, 0x00, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x42, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x58, 0x84, 0x24, 0x12, 0x11, 0x1b, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x89, 0x88, 0x88, 0x52, 0x11, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa8, 0x5a, 0x00, 0x09, 0x99, 0x0f, 0x0f, 0xd9, 0x90, 0x00, 0xf8, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x88, 0x54, 0x12, 0x22, 0x21, 0x1e, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x8a, 0x88, 0x52, 0x11, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x48, 0xcd, 0xa4, 0x55, 0x68, 0x99, 0x84, 0x4a, 0xdf, 0xc5, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x99, 0x54, 0x12, 0x22, 0x21, 0x30, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x88, 0x89, 0x82, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x85, 0x68, 0x85, 0x55, 0x55, 0x44, 0x45, 0x45, 0x65, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0x88, 0x52, 0x22, 0x12, 0x11, 0x60, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0x86, 0x88, 0x84, 0x11, 0x11, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x54, 0x55, 0x85, 0x55, 0x54, 0x55, 0x54, 0x44, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x89, 0x98, 0x52, 0x22, 0x13, 0x11, 0x30, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x68, 0x88, 0xa4, 0x21, 0x11, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0x45, 0x45, 0x55, 0x55, 0x45, 0x55, 0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x99, 0x98, 0x22, 0x22, 0x21, 0x11, 0x26, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0d, 0xa8, 0x88, 0x88, 0x88, 0x41, 0x11, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0xaa, 0x55, 0x55, 0x54, 0x54, 0x44, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x89, 0x98, 0x12, 0x22, 0x23, 0x11, 0x21, 0x16, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xc8, 0x88, 0x88, 0x88, 0x85, 0x41, 0x11, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0x54, 0x48, 0x42, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xc9, 0x94, 0x21, 0x21, 0x11, 0x22, 0x11, 0x11, 0x26, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xd5, 0x55, 0x88, 0x86, 0x88, 0x86, 0x52, 0x11, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0x55, 0x45, 0x42, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0xc9, 0x82, 0x21, 0x22, 0x21, 0x12, 0x11, 0x11, 0x11, 0x00, 0x00,
  0x00, 0x00, 0x08, 0x45, 0x88, 0x58, 0x58, 0x58, 0x88, 0x84, 0x21, 0x12, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0x44, 0x54, 0x53, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x99, 0x99, 0x41, 0x22, 0x22, 0x11, 0x21, 0x11, 0x11, 0x11, 0xe0, 0x00,
  0x00, 0x00, 0x65, 0x55, 0x58, 0x48, 0x58, 0x46, 0x84, 0xa5, 0x42, 0x21, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0x45, 0x55, 0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0x98, 0x41, 0x21, 0x12, 0x21, 0x23, 0x11, 0x11, 0x11, 0xe0, 0x00,
  0x00, 0x00, 0x95, 0x58, 0x55, 0x86, 0x85, 0x88, 0x85, 0x85, 0x42, 0x22, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc4, 0x44, 0x44, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0xcc, 0x94, 0x12, 0x12, 0x21, 0x13, 0x11, 0x11, 0x11, 0x12, 0x00, 0x00,
  0x00, 0x00, 0x09, 0x45, 0x55, 0x55, 0x55, 0x55, 0x84, 0x56, 0x54, 0x42, 0x21, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x45, 0x54, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x9c, 0xc9, 0x52, 0x12, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x17, 0x00, 0x00,
  0x00, 0x00, 0x0a, 0x56, 0x56, 0x85, 0x55, 0x55, 0x55, 0x58, 0x55, 0x44, 0x44, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x24, 0x45, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xcc, 0xc9, 0x22, 0x21, 0x31, 0x31, 0x13, 0x46, 0x31, 0x11, 0x3e, 0x00, 0x00,
  0x00, 0x00, 0x0d, 0x84, 0x54, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x52, 0x44, 0x24, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc4, 0x45, 0x45, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0x94, 0x11, 0x21, 0x21, 0x11, 0x3b, 0x00, 0xe6, 0x22, 0xa0, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x95, 0x48, 0x5a, 0xae, 0xa5, 0x56, 0x55, 0x55, 0x54, 0x44, 0x44, 0x5d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x44, 0x44, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xcc, 0xc9, 0x52, 0x23, 0x22, 0x11, 0x13, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x0f, 0xd0, 0xd0, 0x00, 0x0c, 0x45, 0x55, 0x55, 0x55, 0x44, 0x45, 0x45, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc5, 0x44, 0x54, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0xcc, 0xc5, 0x21, 0x11, 0x12, 0x11, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, 0x55, 0x54, 0x55, 0x54, 0x44, 0x54, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0x24, 0x44, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xcf, 0xcc, 0x81, 0x31, 0x21, 0x31, 0x11, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x44, 0x55, 0x54, 0x55, 0x44, 0x55, 0x55, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0x44, 0x42, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcf, 0xc8, 0x21, 0x21, 0x21, 0x23, 0x13, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x44, 0x44, 0x45, 0x54, 0x54, 0x55, 0x54, 0x49, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc5, 0x24, 0x44, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcf, 0xcc, 0x82, 0x22, 0x13, 0x12, 0x11, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x58, 0x44, 0x54, 0x54, 0x55, 0x55, 0x56, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x25, 0x24, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xfc, 0xc5, 0x21, 0x12, 0x12, 0x31, 0x11, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x44, 0x44, 0x54, 0x48, 0x44, 0x45, 0x58, 0x68, 0x85, 0x89, 0xc0, 0x00, 0x00, 0x00, 0xa5, 0x22, 0x44, 0x27, 0x00, 0x00, 0x00, 0x0f, 0xcc, 0xcc, 0xfc, 0xc9, 0x41, 0x11, 0x21, 0x11, 0x11, 0x11, 0x12, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x44, 0x44, 0x84, 0x44, 0x48, 0x44, 0x55, 0x88, 0x98, 0x58, 0x8c, 0xf0, 0x00, 0x00, 0x94, 0x44, 0x24, 0x15, 0x00, 0x00, 0x0f, 0xc9, 0xcc, 0xcc, 0xcc, 0x85, 0x21, 0x22, 0x22, 0x11, 0x21, 0x11, 0x11, 0x2b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x45, 0x44, 0x45, 0x45, 0x44, 0x44, 0x44, 0x55, 0x89, 0x99, 0x98, 0x88, 0x9c, 0x00, 0x52, 0x24, 0x24, 0x26, 0xcd, 0xd9, 0x89, 0x9c, 0xcc, 0xcc, 0x95, 0x41, 0x13, 0x13, 0x13, 0x22, 0x12, 0x11, 0x21, 0x12, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x44, 0x44, 0x44, 0x44, 0x44, 0x45, 0x54, 0x54, 0x44, 0x48, 0x89, 0x99, 0x99, 0x99, 0x99, 0x54, 0x41, 0x51, 0x54, 0x89, 0x99, 0x9c, 0xc9, 0xc9, 0x85, 0x21, 0x22, 0x12, 0x11, 0x11, 0x13, 0x12, 0x21, 0x11, 0x11, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x44, 0x54, 0x54, 0x54, 0x54, 0x54, 0x44, 0x44, 0x44, 0x44, 0x55, 0x99, 0x9c, 0x9c, 0xc9, 0x44, 0x24, 0x21, 0x45, 0x99, 0x99, 0x99, 0x99, 0x85, 0x42, 0x22, 0x11, 0x21, 0x21, 0x11, 0x11, 0x11, 0x12, 0x11, 0x11, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x55, 0x59, 0x99, 0xc9, 0x52, 0x25, 0x24, 0x45, 0x89, 0x99, 0x88, 0x54, 0x41, 0x12, 0x13, 0x21, 0x11, 0x21, 0x11, 0x31, 0x11, 0x12, 0x11, 0x11, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0x14, 0x44, 0x44, 0x42, 0x15, 0x44, 0x24, 0x44, 0x44, 0x42, 0x44, 0x44, 0x45, 0x58, 0x88, 0x54, 0x14, 0x22, 0x15, 0x88, 0x85, 0x54, 0x11, 0x12, 0x21, 0x21, 0x12, 0x32, 0x11, 0x37, 0x00, 0xa1, 0x11, 0x11, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x42, 0x44, 0x42, 0x45, 0x7b, 0xda, 0x54, 0x24, 0x44, 0x44, 0x22, 0x44, 0x24, 0x44, 0x24, 0x14, 0x12, 0x54, 0x41, 0x22, 0x12, 0x12, 0x22, 0x22, 0x22, 0x22, 0x11, 0x11, 0x13, 0xe0, 0x00, 0x0a, 0x21, 0x11, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x12, 0x14, 0x6b, 0x00, 0x00, 0x06, 0x22, 0x24, 0x24, 0x44, 0x22, 0x42, 0x22, 0x21, 0x51, 0x51, 0x21, 0x24, 0x21, 0x22, 0x22, 0x22, 0x22, 0x11, 0x13, 0x21, 0x13, 0x30, 0x00, 0x00, 0x00, 0x01, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x55, 0x5d, 0x00, 0x00, 0x00, 0x00, 0xc4, 0x24, 0x24, 0x24, 0x42, 0x42, 0x25, 0x42, 0x51, 0x51, 0x44, 0x14, 0x22, 0x22, 0x22, 0x22, 0x12, 0x23, 0x11, 0x21, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x12, 0x42, 0x22, 0x22, 0x42, 0x41, 0x41, 0x42, 0x24, 0x22, 0x22, 0x22, 0x22, 0x22, 0x13, 0x13, 0x11, 0x11, 0x31, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x12, 0x44, 0x24, 0x42, 0x41, 0x41, 0x51, 0x41, 0x41, 0x42, 0x41, 0x22, 0x21, 0x12, 0x11, 0x11, 0x12, 0x11, 0x11, 0x3b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0x24, 0x21, 0x52, 0x24, 0x14, 0x12, 0x14, 0x14, 0x14, 0x11, 0x22, 0x22, 0x12, 0x11, 0x11, 0x11, 0x21, 0x21, 0x11, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x42, 0x51, 0x41, 0x41, 0x15, 0x55, 0x41, 0x41, 0x14, 0x12, 0x22, 0x11, 0x13, 0x3b, 0xbb, 0x61, 0x11, 0x11, 0x11, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x42, 0x24, 0x15, 0x11, 0x70, 0x00, 0xd5, 0x14, 0x21, 0x21, 0x21, 0x13, 0x70, 0x00, 0x00, 0x01, 0x11, 0x21, 0x11, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x21, 0x41, 0x44, 0x11, 0x17, 0x00, 0x00, 0x06, 0x21, 0x22, 0x21, 0x21, 0x17, 0x00, 0x00, 0x00, 0x0a, 0x11, 0x22, 0x11, 0x12, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x11, 0x42, 0x41, 0x41, 0x30, 0x00, 0x00, 0x0a, 0x21, 0x22, 0x13, 0x11, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0x11, 0x11, 0x14, 0xb0, 0x00, 0x00, 0x0d, 0x41, 0x12, 0x12, 0x11, 0x70, 0x00, 0x00, 0x00, 0x00, 0xa1, 0x11, 0x11, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x42, 0x4a, 0x00, 0x00, 0x00, 0x0e, 0x41, 0x21, 0x21, 0x11, 0x40, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x46, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x41, 0x11, 0x11, 0x11, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x11, 0x11, 0x13, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x6a, 0x66, 0x6b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_img_dsc_t img_benchmark_cogwheel_indexed16 = {
  .header.always_zero = 0,
  .header.w = 100,
  .header.h = 100,
  .data_size = 5064,
  .header.cf = LV_IMG_CF_INDEXED_4BIT,
  .data = img_benchmark_cogwheel_indexed16_map,
};

#endif

