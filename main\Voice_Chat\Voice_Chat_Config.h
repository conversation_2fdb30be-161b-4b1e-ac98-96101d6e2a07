#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// Coze platform configuration
// TODO: Replace these with your actual Coze bot credentials
// You can get these from https://www.coze.cn/

// Bot ID from Coze platform
#ifndef CONFIG_COZE_BOT_ID
#define CONFIG_COZE_BOT_ID              "7480000000000000000"  // Replace with your bot ID
#endif

// Access token from Coze platform (should start with "pat_")
#ifndef CONFIG_COZE_ACCESS_TOKEN
#define CONFIG_COZE_ACCESS_TOKEN        "pat_your_access_token_here"  // Replace with your access token
#endif

// User ID for the conversation
#ifndef CONFIG_COZE_USER_ID
#define CONFIG_COZE_USER_ID             "linkpet_user_001"
#endif

// Voice ID for text-to-speech (optional, uses default if not specified)
#ifndef CONFIG_COZE_VOICE_ID
#define CONFIG_COZE_VOICE_ID            "7426720361733144585"  // Default voice ID
#endif

// WiFi configuration for Coze connection
#ifndef CONFIG_WIFI_SSID
#define CONFIG_WIFI_SSID                "Your_WiFi_SSID"  // Replace with your WiFi SSID
#endif

#ifndef CONFIG_WIFI_PASSWORD
#define CONFIG_WIFI_PASSWORD            "Your_WiFi_Password"  // Replace with your WiFi password
#endif

// Voice chat configuration
#define VOICE_CHAT_ENABLE_SUBTITLE      true
#define VOICE_CHAT_ENABLE_WAKEWORD      true
#define VOICE_CHAT_DEFAULT_MODE         VOICE_CHAT_MODE_WAKEWORD

// Audio configuration
#define VOICE_CHAT_AUDIO_SAMPLE_RATE    16000
#define VOICE_CHAT_AUDIO_CHANNELS       1
#define VOICE_CHAT_AUDIO_BITS           16

// Task configuration
#define VOICE_CHAT_TASK_PRIORITY        10
#define VOICE_CHAT_TASK_CORE            1

#ifdef __cplusplus
}
#endif

/*
 * Configuration Instructions:
 * 
 * 1. Create a Bot on Coze Platform:
 *    - Go to https://www.coze.cn/
 *    - Create a new bot
 *    - Copy the Bot ID and replace CONFIG_COZE_BOT_ID
 * 
 * 2. Get Access Token:
 *    - Create an application on Coze platform
 *    - Generate an access token (starts with "pat_")
 *    - Replace CONFIG_COZE_ACCESS_TOKEN
 * 
 * 3. WiFi Configuration:
 *    - Replace CONFIG_WIFI_SSID with your WiFi network name
 *    - Replace CONFIG_WIFI_PASSWORD with your WiFi password
 * 
 * 4. Voice Configuration (Optional):
 *    - You can change the voice ID for different TTS voices
 *    - See Coze documentation for available voice IDs
 * 
 * 5. For production use:
 *    - Consider storing credentials in NVS (Non-Volatile Storage)
 *    - Use menuconfig for configuration management
 *    - Implement secure credential storage
 */
