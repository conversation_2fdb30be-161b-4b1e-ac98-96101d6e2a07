#pragma once

#include "esp_err.h"
#include "esp_log.h"
#include "Voice_Chat.h"
#include "Audio_Adapter.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"

#ifdef __cplusplus
extern "C" {
#endif

// Voice chat manager events
#define VCM_EVENT_CONNECTED         (1 << 0)
#define VCM_EVENT_DISCONNECTED      (1 << 1)
#define VCM_EVENT_LISTENING         (1 << 2)
#define VCM_EVENT_SPEAKING          (1 << 3)
#define VCM_EVENT_ERROR             (1 << 4)
#define VCM_EVENT_WAKEWORD          (1 << 5)

// Voice chat manager configuration
typedef struct {
    char *bot_id;                       // Coze bot ID
    char *access_token;                 // Coze access token
    char *user_id;                      // User ID
    char *voice_id;                     // Voice ID for TTS
    voice_chat_mode_t mode;             // Chat mode
    bool enable_subtitle;               // Enable subtitle display
    bool enable_wakeword;               // Enable wake word detection
    uint8_t task_priority;              // Task priority
    uint8_t task_core;                  // Task core affinity
} voice_chat_manager_config_t;

// Voice chat manager handle
typedef void* voice_chat_manager_handle_t;

// Event callback for UI updates
typedef void (*voice_chat_manager_event_callback_t)(uint32_t events, const char *data, void *ctx);

// Default configuration
#define VOICE_CHAT_MANAGER_DEFAULT_CONFIG() {   \
    .bot_id = NULL,                             \
    .access_token = NULL,                       \
    .user_id = "linkpet_user",                  \
    .voice_id = "7426720361733144585",          \
    .mode = VOICE_CHAT_MODE_WAKEWORD,           \
    .enable_subtitle = true,                    \
    .enable_wakeword = true,                    \
    .task_priority = 10,                        \
    .task_core = 1,                             \
}

/**
 * @brief Initialize voice chat manager
 * 
 * @param config Voice chat manager configuration
 * @param handle Pointer to store the voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_init(const voice_chat_manager_config_t *config, 
                                 voice_chat_manager_handle_t *handle);

/**
 * @brief Deinitialize voice chat manager
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_deinit(voice_chat_manager_handle_t handle);

/**
 * @brief Start voice chat manager
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_start(voice_chat_manager_handle_t handle);

/**
 * @brief Stop voice chat manager
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_stop(voice_chat_manager_handle_t handle);

/**
 * @brief Set event callback for UI updates
 * 
 * @param handle Voice chat manager handle
 * @param callback Event callback function
 * @param ctx User context
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_set_event_callback(voice_chat_manager_handle_t handle,
                                               voice_chat_manager_event_callback_t callback,
                                               void *ctx);

/**
 * @brief Feed audio data from MIC_Speech
 * This function should be called from the MIC_Speech feed_handler
 * 
 * @param handle Voice chat manager handle
 * @param data Audio data from I2S (32-bit)
 * @param len Data length in bytes
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_feed_audio(voice_chat_manager_handle_t handle, 
                                       const int32_t *data, 
                                       size_t len);

/**
 * @brief Handle wake word detection
 * This function should be called when wake word is detected
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_on_wakeword(voice_chat_manager_handle_t handle);

/**
 * @brief Handle command detection timeout
 * This function should be called when command detection times out
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_on_timeout(voice_chat_manager_handle_t handle);

/**
 * @brief Trigger voice chat manually (for button mode)
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_trigger(voice_chat_manager_handle_t handle);

/**
 * @brief Cancel current voice chat session
 * 
 * @param handle Voice chat manager handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_manager_cancel(voice_chat_manager_handle_t handle);

/**
 * @brief Get current voice chat state
 * 
 * @param handle Voice chat manager handle
 * @return voice_chat_state_t Current state
 */
voice_chat_state_t voice_chat_manager_get_state(voice_chat_manager_handle_t handle);

/**
 * @brief Check if voice chat is active
 * 
 * @param handle Voice chat manager handle
 * @return bool True if active
 */
bool voice_chat_manager_is_active(voice_chat_manager_handle_t handle);

#ifdef __cplusplus
}
#endif
