#pragma once

#include "esp_err.h"
#include "esp_log.h"
#include "Voice_Chat.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

// Audio format configuration
#define AUDIO_SAMPLE_RATE       16000
#define AUDIO_CHANNELS          1
#define AUDIO_BITS_PER_SAMPLE   16
#define AUDIO_FRAME_SIZE        640  // 20ms at 16kHz mono 16-bit

// Audio adapter configuration
typedef struct {
    voice_chat_handle_t voice_chat_handle;
    uint32_t sample_rate;
    uint8_t channels;
    uint8_t bits_per_sample;
    size_t frame_size;
    uint8_t task_priority;
    uint8_t task_core;
} audio_adapter_config_t;

// Audio adapter handle
typedef void* audio_adapter_handle_t;

// Default configuration
#define AUDIO_ADAPTER_DEFAULT_CONFIG() {        \
    .voice_chat_handle = NULL,                  \
    .sample_rate = AUDIO_SAMPLE_RATE,           \
    .channels = AUDIO_CHANNELS,                 \
    .bits_per_sample = AUDIO_BITS_PER_SAMPLE,   \
    .frame_size = AUDIO_FRAME_SIZE,             \
    .task_priority = 8,                         \
    .task_core = 0,                             \
}

/**
 * @brief Initialize audio adapter
 * 
 * @param config Audio adapter configuration
 * @param handle Pointer to store the audio adapter handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t audio_adapter_init(const audio_adapter_config_t *config, audio_adapter_handle_t *handle);

/**
 * @brief Deinitialize audio adapter
 * 
 * @param handle Audio adapter handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t audio_adapter_deinit(audio_adapter_handle_t handle);

/**
 * @brief Start audio adapter
 * 
 * @param handle Audio adapter handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t audio_adapter_start(audio_adapter_handle_t handle);

/**
 * @brief Stop audio adapter
 * 
 * @param handle Audio adapter handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t audio_adapter_stop(audio_adapter_handle_t handle);

/**
 * @brief Feed raw audio data to the adapter
 * This function is called by MIC_Speech to provide audio data
 * 
 * @param handle Audio adapter handle
 * @param data Raw audio data (32-bit from I2S)
 * @param len Data length in bytes
 * @return esp_err_t ESP_OK on success
 */
esp_err_t audio_adapter_feed_data(audio_adapter_handle_t handle, const int32_t *data, size_t len);

/**
 * @brief Convert 32-bit I2S data to 16-bit PCM
 * 
 * @param input 32-bit input data
 * @param output 16-bit output buffer
 * @param samples Number of samples to convert
 */
void audio_adapter_convert_32_to_16(const int32_t *input, int16_t *output, size_t samples);

/**
 * @brief Apply audio preprocessing (gain, filtering, etc.)
 * 
 * @param data Audio data buffer
 * @param samples Number of samples
 */
void audio_adapter_preprocess(int16_t *data, size_t samples);

#ifdef __cplusplus
}
#endif
