dependencies:
  espressif/button:
    version: ^4.1.3
  espressif/esp_audio_simple_player:
    version: ^0.9.3
  espressif/esp_coze:
    version: ^0.5
  espressif/esp_hosted:
    rules:
    - if: target in [esp32p4]
    version: ~1.1
  espressif/esp_websocket_client: ^1.2.3
  espressif/esp_wifi_remote:
    matches:
    - if: idf_version <=5.4.0 && target in [esp32p4]
      version: ~0.4
    - if: idf_version >5.4.0 && target in [esp32p4]
      version: ~0.6
  espressif/gmf_ai_audio:
    rules:
    - if: target in [esp32p4, esp32s3, esp32]
    version: ~0.6
  espressif/gmf_audio:
    version: ^0.6
  espressif/gmf_io:
    version: ^0.6
  espressif/gmf_misc:
    version: ^0.6
  protocol_examples_common:
    path: ${IDF_PATH}/examples/common_components/protocol_examples_common
