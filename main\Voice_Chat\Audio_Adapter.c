#include "Audio_Adapter.h"
#include "esp_heap_caps.h"
#include <string.h>
#include <math.h>

static const char *TAG = "Audio_Adapter";

// Audio adapter context
typedef struct {
    audio_adapter_config_t config;
    voice_chat_handle_t voice_chat_handle;
    QueueHandle_t audio_queue;
    TaskHandle_t process_task;
    bool is_running;
    int16_t *frame_buffer;
    size_t frame_buffer_pos;
} audio_adapter_context_t;

// Audio data structure for internal queue
typedef struct {
    int16_t *data;
    size_t len;
} audio_frame_t;

// Forward declarations
static void audio_adapter_process_task(void *param);
static esp_err_t audio_adapter_process_frame(audio_adapter_context_t *ctx, const int16_t *data, size_t len);

esp_err_t audio_adapter_init(const audio_adapter_config_t *config, audio_adapter_handle_t *handle)
{
    if (!config || !handle) {
        ESP_LOGE(TAG, "Invalid parameters");
        return ESP_ERR_INVALID_ARG;
    }

    if (!config->voice_chat_handle) {
        ESP_LOGE(TAG, "Voice chat handle is required");
        return ESP_ERR_INVALID_ARG;
    }

    // Allocate context
    audio_adapter_context_t *ctx = heap_caps_calloc(1, sizeof(audio_adapter_context_t), MALLOC_CAP_8BIT);
    if (!ctx) {
        ESP_LOGE(TAG, "Failed to allocate memory for context");
        return ESP_ERR_NO_MEM;
    }

    // Copy configuration
    memcpy(&ctx->config, config, sizeof(audio_adapter_config_t));
    ctx->voice_chat_handle = config->voice_chat_handle;
    ctx->is_running = false;

    // Allocate frame buffer
    ctx->frame_buffer = heap_caps_malloc(config->frame_size * sizeof(int16_t), MALLOC_CAP_8BIT);
    if (!ctx->frame_buffer) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer");
        free(ctx);
        return ESP_ERR_NO_MEM;
    }
    ctx->frame_buffer_pos = 0;

    // Create audio queue
    ctx->audio_queue = xQueueCreate(20, sizeof(audio_frame_t));
    if (!ctx->audio_queue) {
        ESP_LOGE(TAG, "Failed to create audio queue");
        free(ctx->frame_buffer);
        free(ctx);
        return ESP_ERR_NO_MEM;
    }

    // Create processing task
    BaseType_t task_ret = xTaskCreatePinnedToCore(
        audio_adapter_process_task,
        "audio_adapter",
        4096,
        ctx,
        config->task_priority,
        &ctx->process_task,
        config->task_core
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create processing task");
        vQueueDelete(ctx->audio_queue);
        free(ctx->frame_buffer);
        free(ctx);
        return ESP_ERR_NO_MEM;
    }

    *handle = ctx;
    ESP_LOGI(TAG, "Audio adapter initialized successfully");
    return ESP_OK;
}

esp_err_t audio_adapter_deinit(audio_adapter_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    audio_adapter_context_t *ctx = (audio_adapter_context_t *)handle;
    
    // Stop if running
    if (ctx->is_running) {
        audio_adapter_stop(handle);
    }

    // Delete task
    if (ctx->process_task) {
        vTaskDelete(ctx->process_task);
    }

    // Cleanup queue
    if (ctx->audio_queue) {
        // Clear any remaining audio frames
        audio_frame_t frame;
        while (xQueueReceive(ctx->audio_queue, &frame, 0) == pdTRUE) {
            if (frame.data) {
                free(frame.data);
            }
        }
        vQueueDelete(ctx->audio_queue);
    }

    // Free buffers
    if (ctx->frame_buffer) {
        free(ctx->frame_buffer);
    }

    free(ctx);
    ESP_LOGI(TAG, "Audio adapter deinitialized");
    return ESP_OK;
}

esp_err_t audio_adapter_start(audio_adapter_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    audio_adapter_context_t *ctx = (audio_adapter_context_t *)handle;
    
    if (ctx->is_running) {
        ESP_LOGW(TAG, "Audio adapter already running");
        return ESP_OK;
    }

    ctx->is_running = true;
    ctx->frame_buffer_pos = 0;
    
    ESP_LOGI(TAG, "Audio adapter started");
    return ESP_OK;
}

esp_err_t audio_adapter_stop(audio_adapter_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    audio_adapter_context_t *ctx = (audio_adapter_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_OK;
    }

    ctx->is_running = false;
    
    ESP_LOGI(TAG, "Audio adapter stopped");
    return ESP_OK;
}

esp_err_t audio_adapter_feed_data(audio_adapter_handle_t handle, const int32_t *data, size_t len)
{
    if (!handle || !data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    audio_adapter_context_t *ctx = (audio_adapter_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_ERR_INVALID_STATE;
    }

    // Convert samples count (len is in bytes, we need samples)
    size_t samples = len / sizeof(int32_t);
    
    // Allocate temporary buffer for 16-bit conversion
    int16_t *converted_data = malloc(samples * sizeof(int16_t));
    if (!converted_data) {
        ESP_LOGE(TAG, "Failed to allocate conversion buffer");
        return ESP_ERR_NO_MEM;
    }

    // Convert 32-bit to 16-bit
    audio_adapter_convert_32_to_16(data, converted_data, samples);
    
    // Apply preprocessing
    audio_adapter_preprocess(converted_data, samples);

    // Process the converted data
    esp_err_t ret = audio_adapter_process_frame(ctx, converted_data, samples);
    
    free(converted_data);
    return ret;
}

void audio_adapter_convert_32_to_16(const int32_t *input, int16_t *output, size_t samples)
{
    for (size_t i = 0; i < samples; i++) {
        // Convert from 32-bit to 16-bit by taking the upper 16 bits
        // The MIC_Speech.c shifts right by 14, so we need to shift by 2 more
        output[i] = (int16_t)(input[i] >> 2);
    }
}

void audio_adapter_preprocess(int16_t *data, size_t samples)
{
    // Apply simple gain control and noise gate
    const int16_t noise_threshold = 100;  // Adjust based on your environment
    const float gain = 1.2f;              // Slight amplification
    
    for (size_t i = 0; i < samples; i++) {
        // Apply noise gate
        if (abs(data[i]) < noise_threshold) {
            data[i] = 0;
        } else {
            // Apply gain
            int32_t amplified = (int32_t)(data[i] * gain);
            // Clamp to 16-bit range
            if (amplified > INT16_MAX) {
                data[i] = INT16_MAX;
            } else if (amplified < INT16_MIN) {
                data[i] = INT16_MIN;
            } else {
                data[i] = (int16_t)amplified;
            }
        }
    }
}

static esp_err_t audio_adapter_process_frame(audio_adapter_context_t *ctx, const int16_t *data, size_t samples)
{
    size_t remaining_samples = samples;
    size_t offset = 0;

    while (remaining_samples > 0) {
        // Calculate how many samples we can add to current frame
        size_t frame_remaining = ctx->config.frame_size - ctx->frame_buffer_pos;
        size_t samples_to_copy = (remaining_samples < frame_remaining) ? remaining_samples : frame_remaining;

        // Copy samples to frame buffer
        memcpy(&ctx->frame_buffer[ctx->frame_buffer_pos], &data[offset], samples_to_copy * sizeof(int16_t));
        ctx->frame_buffer_pos += samples_to_copy;
        offset += samples_to_copy;
        remaining_samples -= samples_to_copy;

        // If frame is complete, send it to voice chat
        if (ctx->frame_buffer_pos >= ctx->config.frame_size) {
            // Create frame for queue
            audio_frame_t frame;
            frame.data = malloc(ctx->config.frame_size * sizeof(int16_t));
            if (!frame.data) {
                ESP_LOGE(TAG, "Failed to allocate frame data");
                return ESP_ERR_NO_MEM;
            }
            
            memcpy(frame.data, ctx->frame_buffer, ctx->config.frame_size * sizeof(int16_t));
            frame.len = ctx->config.frame_size * sizeof(int16_t);

            // Send to processing queue
            if (xQueueSend(ctx->audio_queue, &frame, 0) != pdTRUE) {
                ESP_LOGW(TAG, "Audio queue full, dropping frame");
                free(frame.data);
            }

            // Reset frame buffer
            ctx->frame_buffer_pos = 0;
        }
    }

    return ESP_OK;
}

static void audio_adapter_process_task(void *param)
{
    audio_adapter_context_t *ctx = (audio_adapter_context_t *)param;
    audio_frame_t frame;
    
    ESP_LOGI(TAG, "Audio adapter processing task started");
    
    while (true) {
        // Wait for audio frame
        if (xQueueReceive(ctx->audio_queue, &frame, portMAX_DELAY) == pdTRUE) {
            if (frame.data && frame.len > 0) {
                // Send frame to voice chat
                esp_err_t ret = voice_chat_send_audio(ctx->voice_chat_handle, 
                                                    (uint8_t *)frame.data, 
                                                    frame.len);
                if (ret != ESP_OK) {
                    ESP_LOGW(TAG, "Failed to send audio to voice chat: %s", esp_err_to_name(ret));
                }
                
                // Free the frame data
                free(frame.data);
            }
        }
        
        // Check if we should exit
        if (!ctx->is_running) {
            break;
        }
    }
    
    ESP_LOGI(TAG, "Audio adapter processing task ended");
    vTaskDelete(NULL);
}
