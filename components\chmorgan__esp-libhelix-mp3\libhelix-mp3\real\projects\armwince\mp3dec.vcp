# Microsoft eMbedded Visual Tools Project File - Name="mp3dec" - Package Owner=<4>
# Microsoft eMbedded Visual Tools Generated Build File, Format Version 6.02
# ** DO NOT EDIT **

# TARGTYPE "Win32 (WCE ARM) Static Library" 0x8504

CFG=mp3dec - Win32 (WCE ARM) Debug
!MESSAGE This is not a valid makefile. To build this project using NMAKE,
!MESSAGE use the Export Makefile command and run
!MESSAGE 
!MESSAGE NMAKE /f "mp3dec.vcn".
!MESSAGE 
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "mp3dec.vcn" CFG="mp3dec - Win32 (WCE ARM) Debug"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "mp3dec - Win32 (WCE ARM) Release" (based on "Win32 (WCE ARM) Static Library")
!MESSAGE "mp3dec - Win32 (WCE ARM) Debug" (based on "Win32 (WCE ARM) Static Library")
!MESSAGE 

# Begin Project
# PROP AllowPerConfigDependencies 0
# PROP Scc_ProjName ""
# PROP Scc_LocalPath ""
# PROP ATL_Project 2
CPP=xicle3

!IF  "$(CFG)" == "mp3dec - Win32 (WCE ARM) Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "ARMRel"
# PROP BASE Intermediate_Dir "ARMRel"
# PROP BASE CPU_ID "{D6518FFC-710F-11D3-99F2-00105A0DF099}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "ARMRel"
# PROP Intermediate_Dir "ARMRel_obj"
# PROP CPU_ID "{D6518FFC-710F-11D3-99F2-00105A0DF099}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
# ADD BASE CPP /nologo /W3 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "NDEBUG" /D "ARM" /D "_ARM_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /Oxs /M$(CECrtMT) /c
# ADD CPP /nologo /W3 /Zi /O2 /I "..\..\..\..\..\..\..\common\runtime\pub" /I "..\..\..\..\..\..\..\common\include" /I "..\..\..\pub" /D "NDEBUG" /D "_WINDOWS" /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "ARM" /D "_ARM_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /Oxs /M$(CECrtMT) /c
# SUBTRACT CPP /YX
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "mp3dec - Win32 (WCE ARM) Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "ARMDbg"
# PROP BASE Intermediate_Dir "ARMDbg"
# PROP BASE CPU_ID "{D6518FFC-710F-11D3-99F2-00105A0DF099}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "ARMDbg"
# PROP Intermediate_Dir "ARMDbg"
# PROP CPU_ID "{D6518FFC-710F-11D3-99F2-00105A0DF099}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
# ADD BASE CPP /nologo /W3 /Zi /Od /D "DEBUG" /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "ARM" /D "_ARM_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /M$(CECrtMTDebug) /c
# ADD CPP /nologo /W3 /Zi /Od /I "..\..\..\pub" /I "..\..\..\..\..\..\..\common\runtime\pub" /I "..\..\..\..\..\..\..\common\include" /D "DEBUG" /D "_WINDOWS" /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "ARM" /D "_ARM_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /M$(CECrtMTDebug) /c
# SUBTRACT CPP /YX
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ENDIF 

# Begin Target

# Name "mp3dec - Win32 (WCE ARM) Release"
# Name "mp3dec - Win32 (WCE ARM) Debug"
# Begin Group "Source Files"

# PROP Default_Filter "cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
# Begin Group "general"

# PROP Default_Filter ""
# Begin Source File

SOURCE=..\..\..\mp3dec.c
DEP_CPP_MP3DE=\
	"..\..\..\..\..\..\..\common\include\hxbastsd.h"\
	"..\..\..\..\..\..\..\common\include\hxtypes.h"\
	"..\..\..\..\..\..\..\common\include\platform\symbian\symbiantypes.h"\
	"..\..\..\..\..\..\..\common\runtime\pub\hlxclib\stdlib.h"\
	"..\..\..\..\..\..\..\common\runtime\pub\hlxclib\string.h"\
	"..\..\..\..\..\..\..\common\runtime\pub\platform\openwave\hx_op_stdc.h"\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	
NODEP_CPP_MP3DE=\
	"..\..\..\..\..\..\..\common\include\types\vxTypesOld.h"\
	"..\..\..\..\..\..\..\common\include\vxWorks.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\..\mp3tabs.c
DEP_CPP_MP3TA=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	
# End Source File
# End Group
# Begin Group "csource"

# PROP Default_Filter ""
# Begin Source File

SOURCE=..\..\bitstream.c
DEP_CPP_BITST=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\buffers.c
DEP_CPP_BUFFE=\
	"..\..\..\..\..\..\..\common\include\hxbastsd.h"\
	"..\..\..\..\..\..\..\common\include\hxtypes.h"\
	"..\..\..\..\..\..\..\common\include\platform\symbian\symbiantypes.h"\
	"..\..\..\..\..\..\..\common\runtime\pub\hlxclib\stdlib.h"\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\coder.h"\
	
NODEP_CPP_BUFFE=\
	"..\..\..\..\..\..\..\common\include\types\vxTypesOld.h"\
	"..\..\..\..\..\..\..\common\include\vxWorks.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\dct32.c
DEP_CPP_DCT32=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\dequant.c
DEP_CPP_DEQUA=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\dqchan.c
DEP_CPP_DQCHA=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\huffman.c
DEP_CPP_HUFFM=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\hufftabs.c
DEP_CPP_HUFFT=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\imdct.c
DEP_CPP_IMDCT=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\scalfact.c
DEP_CPP_SCALF=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\stproc.c
DEP_CPP_STPRO=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\subband.c
DEP_CPP_SUBBA=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\assembly.h"\
	"..\..\coder.h"\
	
# End Source File
# Begin Source File

SOURCE=..\..\trigtabs.c
DEP_CPP_TRIGT=\
	"..\..\..\pub\mp3common.h"\
	"..\..\..\pub\mp3dec.h"\
	"..\..\..\pub\statname.h"\
	"..\..\coder.h"\
	
# End Source File
# End Group
# Begin Group "assembly"

# PROP Default_Filter ""
# Begin Source File

SOURCE=..\..\arm\asmmisc.s

!IF  "$(CFG)" == "mp3dec - Win32 (WCE ARM) Release"

# PROP Ignore_Default_Tool 1
# Begin Custom Build - Performing Custom Build Step on $(InputPath)
IntDir=.\ARMRel_obj
InputPath=..\..\arm\asmmisc.s
InputName=asmmisc

"$(IntDir)\$(InputName).obj" : $(SOURCE) "$(INTDIR)" "$(OUTDIR)"
	"$(EVCROOT)\wce300\bin\armasm" $(InputPath) $(IntDir)\$(InputName).obj

# End Custom Build

!ELSEIF  "$(CFG)" == "mp3dec - Win32 (WCE ARM) Debug"

# PROP Ignore_Default_Tool 1
# Begin Custom Build - Performing Custom Build Step on $(InputPath)
IntDir=.\ARMDbg
InputPath=..\..\arm\asmmisc.s
InputName=asmmisc

"$(IntDir)\$(InputName).obj" : $(SOURCE) "$(INTDIR)" "$(OUTDIR)"
	"$(EVCROOT)\wce300\bin\armasm" $(InputPath) $(IntDir)\$(InputName).obj

# End Custom Build

!ENDIF 

# End Source File
# Begin Source File

SOURCE=..\..\arm\asmpoly.s

!IF  "$(CFG)" == "mp3dec - Win32 (WCE ARM) Release"

# PROP Ignore_Default_Tool 1
# Begin Custom Build
IntDir=.\ARMRel_obj
InputPath=..\..\arm\asmpoly.s
InputName=asmpoly

"$(IntDir)\$(InputName).obj" : $(SOURCE) "$(INTDIR)" "$(OUTDIR)"
	"$(EVCROOT)\wce300\bin\armasm" $(InputPath) $(IntDir)\$(InputName).obj

# End Custom Build

!ELSEIF  "$(CFG)" == "mp3dec - Win32 (WCE ARM) Debug"

# PROP Ignore_Default_Tool 1
# Begin Custom Build
IntDir=.\ARMDbg
InputPath=..\..\arm\asmpoly.s
InputName=asmpoly

"$(IntDir)\$(InputName).obj" : $(SOURCE) "$(INTDIR)" "$(OUTDIR)"
	"$(EVCROOT)\wce300\bin\armasm" $(InputPath) $(IntDir)\$(InputName).obj

# End Custom Build

!ENDIF 

# End Source File
# End Group
# End Group
# End Target
# End Project
