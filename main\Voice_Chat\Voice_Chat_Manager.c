#include "Voice_Chat_Manager.h"
#include "esp_heap_caps.h"
#include <string.h>

static const char *TAG = "Voice_Chat_Manager";

// Voice chat manager context
typedef struct {
    voice_chat_manager_config_t config;
    voice_chat_handle_t voice_chat_handle;
    audio_adapter_handle_t audio_adapter_handle;
    voice_chat_manager_event_callback_t event_callback;
    void *event_ctx;
    EventGroupHandle_t event_group;
    bool is_running;
    bool is_active;
    bool wakeword_detected;
} voice_chat_manager_context_t;

// Forward declarations
static void voice_chat_manager_voice_event_callback(voice_chat_state_t state, const char *data, void *ctx);

esp_err_t voice_chat_manager_init(const voice_chat_manager_config_t *config, 
                                 voice_chat_manager_handle_t *handle)
{
    if (!config || !handle) {
        ESP_LOGE(TAG, "Invalid parameters");
        return ESP_ERR_INVALID_ARG;
    }

    if (!config->bot_id || !config->access_token) {
        ESP_LOGE(TAG, "Bot ID and access token are required");
        return ESP_ERR_INVALID_ARG;
    }

    // Allocate context
    voice_chat_manager_context_t *ctx = heap_caps_calloc(1, sizeof(voice_chat_manager_context_t), MALLOC_CAP_8BIT);
    if (!ctx) {
        ESP_LOGE(TAG, "Failed to allocate memory for context");
        return ESP_ERR_NO_MEM;
    }

    // Copy configuration
    memcpy(&ctx->config, config, sizeof(voice_chat_manager_config_t));
    ctx->is_running = false;
    ctx->is_active = false;
    ctx->wakeword_detected = false;

    // Create event group
    ctx->event_group = xEventGroupCreate();
    if (!ctx->event_group) {
        ESP_LOGE(TAG, "Failed to create event group");
        free(ctx);
        return ESP_ERR_NO_MEM;
    }

    // Initialize voice chat
    voice_chat_config_t voice_config = VOICE_CHAT_DEFAULT_CONFIG();
    voice_config.bot_id = ctx->config.bot_id;
    voice_config.access_token = ctx->config.access_token;
    voice_config.user_id = ctx->config.user_id;
    voice_config.voice_id = ctx->config.voice_id;
    voice_config.mode = ctx->config.mode;
    voice_config.enable_subtitle = ctx->config.enable_subtitle;
    voice_config.task_priority = ctx->config.task_priority;
    voice_config.task_core = ctx->config.task_core;

    esp_err_t ret = voice_chat_init(&voice_config, &ctx->voice_chat_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize voice chat: %s", esp_err_to_name(ret));
        vEventGroupDelete(ctx->event_group);
        free(ctx);
        return ret;
    }

    // Set voice chat event callback
    ret = voice_chat_set_event_callback(ctx->voice_chat_handle, 
                                       voice_chat_manager_voice_event_callback, 
                                       ctx);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set voice chat event callback: %s", esp_err_to_name(ret));
        voice_chat_deinit(ctx->voice_chat_handle);
        vEventGroupDelete(ctx->event_group);
        free(ctx);
        return ret;
    }

    // Initialize audio adapter
    audio_adapter_config_t adapter_config = AUDIO_ADAPTER_DEFAULT_CONFIG();
    adapter_config.voice_chat_handle = ctx->voice_chat_handle;
    adapter_config.task_priority = ctx->config.task_priority - 1;  // Lower priority than voice chat
    adapter_config.task_core = (ctx->config.task_core == 0) ? 1 : 0;  // Use different core

    ret = audio_adapter_init(&adapter_config, &ctx->audio_adapter_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize audio adapter: %s", esp_err_to_name(ret));
        voice_chat_deinit(ctx->voice_chat_handle);
        vEventGroupDelete(ctx->event_group);
        free(ctx);
        return ret;
    }

    *handle = ctx;
    ESP_LOGI(TAG, "Voice chat manager initialized successfully");
    return ESP_OK;
}

esp_err_t voice_chat_manager_deinit(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    
    // Stop if running
    if (ctx->is_running) {
        voice_chat_manager_stop(handle);
    }

    // Cleanup audio adapter
    if (ctx->audio_adapter_handle) {
        audio_adapter_deinit(ctx->audio_adapter_handle);
    }

    // Cleanup voice chat
    if (ctx->voice_chat_handle) {
        voice_chat_deinit(ctx->voice_chat_handle);
    }

    // Cleanup event group
    if (ctx->event_group) {
        vEventGroupDelete(ctx->event_group);
    }

    free(ctx);
    ESP_LOGI(TAG, "Voice chat manager deinitialized");
    return ESP_OK;
}

esp_err_t voice_chat_manager_start(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    
    if (ctx->is_running) {
        ESP_LOGW(TAG, "Voice chat manager already running");
        return ESP_OK;
    }

    // Start voice chat
    esp_err_t ret = voice_chat_start(ctx->voice_chat_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start voice chat: %s", esp_err_to_name(ret));
        return ret;
    }

    // Start audio adapter
    ret = audio_adapter_start(ctx->audio_adapter_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start audio adapter: %s", esp_err_to_name(ret));
        voice_chat_stop(ctx->voice_chat_handle);
        return ret;
    }

    ctx->is_running = true;
    ctx->is_active = false;
    ctx->wakeword_detected = false;
    
    // Notify UI
    if (ctx->event_callback) {
        ctx->event_callback(VCM_EVENT_CONNECTED, NULL, ctx->event_ctx);
    }
    
    ESP_LOGI(TAG, "Voice chat manager started");
    return ESP_OK;
}

esp_err_t voice_chat_manager_stop(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_OK;
    }

    // Stop audio adapter
    audio_adapter_stop(ctx->audio_adapter_handle);

    // Stop voice chat
    voice_chat_stop(ctx->voice_chat_handle);

    ctx->is_running = false;
    ctx->is_active = false;
    ctx->wakeword_detected = false;
    
    // Notify UI
    if (ctx->event_callback) {
        ctx->event_callback(VCM_EVENT_DISCONNECTED, NULL, ctx->event_ctx);
    }
    
    ESP_LOGI(TAG, "Voice chat manager stopped");
    return ESP_OK;
}

esp_err_t voice_chat_manager_set_event_callback(voice_chat_manager_handle_t handle,
                                               voice_chat_manager_event_callback_t callback,
                                               void *ctx)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *manager_ctx = (voice_chat_manager_context_t *)handle;
    manager_ctx->event_callback = callback;
    manager_ctx->event_ctx = ctx;
    
    return ESP_OK;
}

esp_err_t voice_chat_manager_feed_audio(voice_chat_manager_handle_t handle, 
                                       const int32_t *data, 
                                       size_t len)
{
    if (!handle || !data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_ERR_INVALID_STATE;
    }

    // Only feed audio if we're in active conversation mode or continuous mode
    if (ctx->config.mode == VOICE_CHAT_MODE_CONTINUOUS || 
        (ctx->config.mode == VOICE_CHAT_MODE_WAKEWORD && ctx->is_active)) {
        
        return audio_adapter_feed_data(ctx->audio_adapter_handle, data, len);
    }

    return ESP_OK;
}

esp_err_t voice_chat_manager_on_wakeword(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Wake word detected");
    
    ctx->wakeword_detected = true;
    ctx->is_active = true;
    
    // Notify UI
    if (ctx->event_callback) {
        ctx->event_callback(VCM_EVENT_WAKEWORD | VCM_EVENT_LISTENING, NULL, ctx->event_ctx);
    }
    
    return ESP_OK;
}

esp_err_t voice_chat_manager_on_timeout(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Command detection timeout");
    
    ctx->is_active = false;
    ctx->wakeword_detected = false;
    
    // Cancel any ongoing conversation
    voice_chat_cancel(ctx->voice_chat_handle);
    
    // Notify UI
    if (ctx->event_callback) {
        ctx->event_callback(VCM_EVENT_CONNECTED, NULL, ctx->event_ctx);
    }
    
    return ESP_OK;
}

esp_err_t voice_chat_manager_trigger(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;

    if (!ctx->is_running || ctx->config.mode != VOICE_CHAT_MODE_BUTTON) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Voice chat triggered manually");

    ctx->is_active = true;

    // Trigger voice chat
    esp_err_t ret = voice_chat_trigger(ctx->voice_chat_handle);
    if (ret == ESP_OK) {
        // Notify UI
        if (ctx->event_callback) {
            ctx->event_callback(VCM_EVENT_LISTENING, NULL, ctx->event_ctx);
        }
    }

    return ret;
}

esp_err_t voice_chat_manager_cancel(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;

    if (!ctx->is_running) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Voice chat cancelled");

    ctx->is_active = false;
    ctx->wakeword_detected = false;

    // Cancel voice chat
    esp_err_t ret = voice_chat_cancel(ctx->voice_chat_handle);
    if (ret == ESP_OK) {
        // Notify UI
        if (ctx->event_callback) {
            ctx->event_callback(VCM_EVENT_CONNECTED, NULL, ctx->event_ctx);
        }
    }

    return ret;
}

voice_chat_state_t voice_chat_manager_get_state(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return VOICE_CHAT_STATE_ERROR;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    return voice_chat_get_state(ctx->voice_chat_handle);
}

bool voice_chat_manager_is_active(voice_chat_manager_handle_t handle)
{
    if (!handle) {
        return false;
    }

    voice_chat_manager_context_t *ctx = (voice_chat_manager_context_t *)handle;
    return ctx->is_active;
}

// Internal callback function
static void voice_chat_manager_voice_event_callback(voice_chat_state_t state, const char *data, void *ctx)
{
    if (!ctx) {
        return;
    }

    voice_chat_manager_context_t *manager_ctx = (voice_chat_manager_context_t *)ctx;

    uint32_t events = 0;

    switch (state) {
        case VOICE_CHAT_STATE_IDLE:
            events = VCM_EVENT_DISCONNECTED;
            manager_ctx->is_active = false;
            break;

        case VOICE_CHAT_STATE_CONNECTING:
            // No specific event for connecting
            break;

        case VOICE_CHAT_STATE_CONNECTED:
            events = VCM_EVENT_CONNECTED;
            if (manager_ctx->config.mode != VOICE_CHAT_MODE_CONTINUOUS) {
                manager_ctx->is_active = false;
            }
            break;

        case VOICE_CHAT_STATE_LISTENING:
            events = VCM_EVENT_LISTENING;
            manager_ctx->is_active = true;
            break;

        case VOICE_CHAT_STATE_SPEAKING:
            events = VCM_EVENT_SPEAKING;
            break;

        case VOICE_CHAT_STATE_ERROR:
            events = VCM_EVENT_ERROR;
            manager_ctx->is_active = false;
            break;
    }

    // Notify UI callback if set
    if (manager_ctx->event_callback && events != 0) {
        manager_ctx->event_callback(events, data, manager_ctx->event_ctx);
    }
}
