# ESP-DSP Examples

This directory contains a range of examples for ESP-DSP library.

These examples are intended to demonstrate part of ESP-DSP functionality (e.g. initialization, execution) and to provide code that you can copy and adapt into your own projects.

See the [README.md](../README.md) file in the upper level directory for more information about ESP-DSP.

# Example Layout

The examples are grouped into subdirectories by category. Each category directory contains one or more example projects:

* [Dot Product Calculation](./dotprod/README.md) Example
* [Basic Math Operations](./basic_math/README.md) Example
* [FFT](./fft/README.md) Example
* [Matrix](./matrix/README.md) Example
* [FFT Window](./fft_window/README.md) Example
* [IIR Filter](./iir/README.md) Example
* [Kalman Filter](./kalman/README.md) Example
* [FIR Filter](.fir/README.md) Example
