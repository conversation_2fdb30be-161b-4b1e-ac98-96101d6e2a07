#include "Voice_Chat_Init.h"
#include "esp_log.h"
#include "esp_heap_caps.h"

static const char *TAG = "Voice_Chat_Init";

// Global voice chat system state
static voice_chat_manager_handle_t g_voice_chat_manager_handle = NULL;
static bool g_voice_chat_system_initialized = false;

// External reference to global variable in MIC_Speech.c
extern void* g_voice_chat_manager;

esp_err_t voice_chat_system_init(void)
{
    if (g_voice_chat_system_initialized) {
        ESP_LOGW(TAG, "Voice chat system already initialized");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Initializing voice chat system...");

    // Configure voice chat manager
    voice_chat_manager_config_t config = VOICE_CHAT_MANAGER_DEFAULT_CONFIG();
    config.bot_id = CONFIG_COZE_BOT_ID;
    config.access_token = CONFIG_COZE_ACCESS_TOKEN;
    config.user_id = CONFIG_COZE_USER_ID;
    config.voice_id = CONFIG_COZE_VOICE_ID;
    config.enable_subtitle = VOICE_CHAT_ENABLE_SUBTITLE;
    config.enable_wakeword = VOICE_CHAT_ENABLE_WAKEWORD;
    config.mode = VOICE_CHAT_DEFAULT_MODE;
    config.task_priority = VOICE_CHAT_TASK_PRIORITY;
    config.task_core = VOICE_CHAT_TASK_CORE;

    // Initialize voice chat manager
    esp_err_t ret = voice_chat_manager_init(&config, &g_voice_chat_manager_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize voice chat manager: %s", esp_err_to_name(ret));
        return ret;
    }

    // Set event callback for UI integration
    ret = voice_chat_manager_set_event_callback(g_voice_chat_manager_handle,
                                               voice_chat_system_ui_event_callback,
                                               NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set event callback: %s", esp_err_to_name(ret));
        voice_chat_manager_deinit(g_voice_chat_manager_handle);
        g_voice_chat_manager_handle = NULL;
        return ret;
    }

    // Set global reference for MIC_Speech integration
    g_voice_chat_manager = g_voice_chat_manager_handle;

    g_voice_chat_system_initialized = true;
    ESP_LOGI(TAG, "Voice chat system initialized successfully");
    return ESP_OK;
}

esp_err_t voice_chat_system_deinit(void)
{
    if (!g_voice_chat_system_initialized) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Deinitializing voice chat system...");

    // Clear global reference
    g_voice_chat_manager = NULL;

    // Deinitialize voice chat manager
    if (g_voice_chat_manager_handle) {
        voice_chat_manager_deinit(g_voice_chat_manager_handle);
        g_voice_chat_manager_handle = NULL;
    }

    g_voice_chat_system_initialized = false;
    ESP_LOGI(TAG, "Voice chat system deinitialized");
    return ESP_OK;
}

esp_err_t voice_chat_system_start(void)
{
    if (!g_voice_chat_system_initialized || !g_voice_chat_manager_handle) {
        ESP_LOGE(TAG, "Voice chat system not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Starting voice chat system...");

    esp_err_t ret = voice_chat_manager_start(g_voice_chat_manager_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start voice chat manager: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "Voice chat system started successfully");
    return ESP_OK;
}

esp_err_t voice_chat_system_stop(void)
{
    if (!g_voice_chat_system_initialized || !g_voice_chat_manager_handle) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Stopping voice chat system...");

    esp_err_t ret = voice_chat_manager_stop(g_voice_chat_manager_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop voice chat manager: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "Voice chat system stopped");
    return ESP_OK;
}

bool voice_chat_system_is_initialized(void)
{
    return g_voice_chat_system_initialized;
}

voice_chat_manager_handle_t voice_chat_system_get_manager(void)
{
    return g_voice_chat_manager_handle;
}

void voice_chat_system_ui_event_callback(uint32_t events, const char *data, void *ctx)
{
    // Handle voice chat events for UI updates
    if (events & VCM_EVENT_CONNECTED) {
        ESP_LOGI(TAG, "Voice chat connected");
        // TODO: Update UI to show connected status
    }
    
    if (events & VCM_EVENT_DISCONNECTED) {
        ESP_LOGI(TAG, "Voice chat disconnected");
        // TODO: Update UI to show disconnected status
    }
    
    if (events & VCM_EVENT_WAKEWORD) {
        ESP_LOGI(TAG, "Wake word detected");
        // TODO: Update UI to show wake word detected (e.g., LED animation)
    }
    
    if (events & VCM_EVENT_LISTENING) {
        ESP_LOGI(TAG, "Voice chat listening");
        // TODO: Update UI to show listening status (e.g., microphone icon)
    }
    
    if (events & VCM_EVENT_SPEAKING) {
        ESP_LOGI(TAG, "Voice chat speaking");
        // TODO: Update UI to show speaking status (e.g., speaker icon)
    }
    
    if (events & VCM_EVENT_ERROR) {
        ESP_LOGE(TAG, "Voice chat error: %s", data ? data : "Unknown error");
        // TODO: Update UI to show error status
    }
    
    // Handle subtitle data
    if (data && strlen(data) > 0) {
        ESP_LOGI(TAG, "Subtitle: %s", data);
        // TODO: Update UI to display subtitle text
    }
}

// Helper function to check credentials
static bool voice_chat_check_credentials(void)
{
    // Check if credentials are properly configured
    if (strcmp(CONFIG_COZE_BOT_ID, "7480000000000000000") == 0) {
        ESP_LOGW(TAG, "Using default bot ID - please configure your actual bot ID");
        return false;
    }
    
    if (strcmp(CONFIG_COZE_ACCESS_TOKEN, "pat_your_access_token_here") == 0) {
        ESP_LOGW(TAG, "Using default access token - please configure your actual access token");
        return false;
    }
    
    return true;
}

// Function to validate configuration before initialization
esp_err_t voice_chat_system_validate_config(void)
{
    ESP_LOGI(TAG, "Validating voice chat configuration...");
    
    if (!voice_chat_check_credentials()) {
        ESP_LOGE(TAG, "Invalid credentials configuration");
        ESP_LOGE(TAG, "Please update Voice_Chat_Config.h with your actual Coze bot credentials");
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "Configuration validation passed");
    return ESP_OK;
}
