#pragma once

#include "esp_err.h"
#include "Voice_Chat_Manager.h"
#include "Voice_Chat_Config.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize voice chat system
 * This function initializes the complete voice chat system including:
 * - Voice chat manager
 * - Audio adapter
 * - Integration with MIC_Speech
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_system_init(void);

/**
 * @brief Deinitialize voice chat system
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_system_deinit(void);

/**
 * @brief Start voice chat system
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_system_start(void);

/**
 * @brief Stop voice chat system
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_system_stop(void);

/**
 * @brief Check if voice chat system is initialized
 * 
 * @return bool True if initialized
 */
bool voice_chat_system_is_initialized(void);

/**
 * @brief Get voice chat manager handle
 * 
 * @return voice_chat_manager_handle_t Handle or NULL if not initialized
 */
voice_chat_manager_handle_t voice_chat_system_get_manager(void);

/**
 * @brief Validate voice chat system configuration
 * This function checks if the configuration is valid before initialization
 *
 * @return esp_err_t ESP_OK if configuration is valid
 */
esp_err_t voice_chat_system_validate_config(void);

/**
 * @brief Voice chat system event callback for UI integration
 * This callback can be used to update UI elements based on voice chat events
 *
 * @param events Event flags (VCM_EVENT_*)
 * @param data Event data (subtitle text, error messages, etc.)
 * @param ctx User context
 */
void voice_chat_system_ui_event_callback(uint32_t events, const char *data, void *ctx);

#ifdef __cplusplus
}
#endif
