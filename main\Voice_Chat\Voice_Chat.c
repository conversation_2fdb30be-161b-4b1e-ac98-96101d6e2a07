#include "Voice_Chat.h"
#include "PCM5101.h"
#include "esp_heap_caps.h"
#include <string.h>

static const char *TAG = "Voice_Chat";

// Voice chat context structure
typedef struct {
    esp_coze_chat_handle_t coze_handle;
    voice_chat_config_t config;
    voice_chat_state_t state;
    voice_chat_event_callback_t event_callback;
    void *event_ctx;
    EventGroupHandle_t event_group;
    QueueHandle_t audio_queue;
    TaskHandle_t audio_task;
    bool is_running;
} voice_chat_context_t;

// Audio data structure for queue
typedef struct {
    uint8_t *data;
    size_t len;
} audio_data_t;

// Forward declarations
static void voice_chat_audio_callback(char *data, int len, void *ctx);
static void voice_chat_event_handler(esp_coze_chat_event_t event, char *event_data, void *ctx);
static void voice_chat_audio_task(void *param);
static esp_err_t voice_chat_set_state(voice_chat_context_t *ctx, voice_chat_state_t new_state);

esp_err_t voice_chat_init(const voice_chat_config_t *config, voice_chat_handle_t *handle)
{
    if (!config || !handle) {
        ESP_LOGE(TAG, "Invalid parameters");
        return ESP_ERR_INVALID_ARG;
    }

    if (!config->bot_id || !config->access_token) {
        ESP_LOGE(TAG, "Bot ID and access token are required");
        return ESP_ERR_INVALID_ARG;
    }

    // Allocate context
    voice_chat_context_t *ctx = heap_caps_calloc(1, sizeof(voice_chat_context_t), MALLOC_CAP_8BIT);
    if (!ctx) {
        ESP_LOGE(TAG, "Failed to allocate memory for context");
        return ESP_ERR_NO_MEM;
    }

    // Copy configuration
    memcpy(&ctx->config, config, sizeof(voice_chat_config_t));
    ctx->state = VOICE_CHAT_STATE_IDLE;
    ctx->is_running = false;

    // Create event group and queue
    ctx->event_group = xEventGroupCreate();
    if (!ctx->event_group) {
        ESP_LOGE(TAG, "Failed to create event group");
        free(ctx);
        return ESP_ERR_NO_MEM;
    }

    ctx->audio_queue = xQueueCreate(10, sizeof(audio_data_t));
    if (!ctx->audio_queue) {
        ESP_LOGE(TAG, "Failed to create audio queue");
        vEventGroupDelete(ctx->event_group);
        free(ctx);
        return ESP_ERR_NO_MEM;
    }

    // Configure Coze chat
    esp_coze_chat_config_t chat_config = ESP_COZE_CHAT_DEFAULT_CONFIG();
    chat_config.bot_id = ctx->config.bot_id;
    chat_config.access_token = ctx->config.access_token;
    chat_config.user_id = ctx->config.user_id;
    chat_config.voice_id = ctx->config.voice_id;
    chat_config.enable_subtitle = ctx->config.enable_subtitle;
    chat_config.audio_callback = voice_chat_audio_callback;
    chat_config.event_callback = voice_chat_event_handler;
    chat_config.audio_callback_ctx = ctx;
    chat_config.event_callback_ctx = ctx;
    
    // Set mode based on configuration
    if (ctx->config.mode == VOICE_CHAT_MODE_BUTTON) {
        chat_config.mode = ESP_COZE_CHAT_NORMAL_MODE;
    } else {
        chat_config.mode = ESP_COZE_CHAT_SPEECH_INTERRUPT_MODE;
    }

    // Initialize Coze chat
    esp_err_t ret = esp_coze_chat_init(&chat_config, &ctx->coze_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize Coze chat: %s", esp_err_to_name(ret));
        vQueueDelete(ctx->audio_queue);
        vEventGroupDelete(ctx->event_group);
        free(ctx);
        return ret;
    }

    // Create audio processing task
    BaseType_t task_ret = xTaskCreatePinnedToCore(
        voice_chat_audio_task,
        "voice_chat_audio",
        4096,
        ctx,
        ctx->config.task_priority,
        &ctx->audio_task,
        ctx->config.task_core
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create audio task");
        esp_coze_chat_deinit(ctx->coze_handle);
        vQueueDelete(ctx->audio_queue);
        vEventGroupDelete(ctx->event_group);
        free(ctx);
        return ESP_ERR_NO_MEM;
    }

    *handle = ctx;
    ESP_LOGI(TAG, "Voice chat initialized successfully");
    return ESP_OK;
}

esp_err_t voice_chat_deinit(voice_chat_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;
    
    // Stop if running
    if (ctx->is_running) {
        voice_chat_stop(handle);
    }

    // Delete task
    if (ctx->audio_task) {
        vTaskDelete(ctx->audio_task);
    }

    // Cleanup Coze chat
    if (ctx->coze_handle) {
        esp_coze_chat_deinit(ctx->coze_handle);
    }

    // Cleanup queue and event group
    if (ctx->audio_queue) {
        // Clear any remaining audio data
        audio_data_t audio_data;
        while (xQueueReceive(ctx->audio_queue, &audio_data, 0) == pdTRUE) {
            if (audio_data.data) {
                free(audio_data.data);
            }
        }
        vQueueDelete(ctx->audio_queue);
    }

    if (ctx->event_group) {
        vEventGroupDelete(ctx->event_group);
    }

    free(ctx);
    ESP_LOGI(TAG, "Voice chat deinitialized");
    return ESP_OK;
}

esp_err_t voice_chat_start(voice_chat_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;
    
    if (ctx->is_running) {
        ESP_LOGW(TAG, "Voice chat already running");
        return ESP_OK;
    }

    voice_chat_set_state(ctx, VOICE_CHAT_STATE_CONNECTING);
    
    esp_err_t ret = esp_coze_chat_start(ctx->coze_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start Coze chat: %s", esp_err_to_name(ret));
        voice_chat_set_state(ctx, VOICE_CHAT_STATE_ERROR);
        return ret;
    }

    ctx->is_running = true;
    xEventGroupSetBits(ctx->event_group, VOICE_CHAT_EVENT_CONNECTED);
    
    ESP_LOGI(TAG, "Voice chat started");
    return ESP_OK;
}

esp_err_t voice_chat_stop(voice_chat_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;
    
    if (!ctx->is_running) {
        return ESP_OK;
    }

    esp_err_t ret = esp_coze_chat_stop(ctx->coze_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop Coze chat: %s", esp_err_to_name(ret));
    }

    ctx->is_running = false;
    voice_chat_set_state(ctx, VOICE_CHAT_STATE_IDLE);
    xEventGroupSetBits(ctx->event_group, VOICE_CHAT_EVENT_DISCONNECTED);
    
    ESP_LOGI(TAG, "Voice chat stopped");
    return ESP_OK;
}

esp_err_t voice_chat_send_audio(voice_chat_handle_t handle, const uint8_t *data, size_t len)
{
    if (!handle || !data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;
    
    if (!ctx->is_running || ctx->state != VOICE_CHAT_STATE_CONNECTED) {
        return ESP_ERR_INVALID_STATE;
    }

    // Create audio data structure
    audio_data_t audio_data;
    audio_data.data = malloc(len);
    if (!audio_data.data) {
        ESP_LOGE(TAG, "Failed to allocate memory for audio data");
        return ESP_ERR_NO_MEM;
    }
    
    memcpy(audio_data.data, data, len);
    audio_data.len = len;

    // Send to queue
    if (xQueueSend(ctx->audio_queue, &audio_data, 0) != pdTRUE) {
        ESP_LOGW(TAG, "Audio queue full, dropping data");
        free(audio_data.data);
        return ESP_ERR_TIMEOUT;
    }

    return ESP_OK;
}

esp_err_t voice_chat_set_event_callback(voice_chat_handle_t handle,
                                       voice_chat_event_callback_t callback,
                                       void *ctx)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *voice_ctx = (voice_chat_context_t *)handle;
    voice_ctx->event_callback = callback;
    voice_ctx->event_ctx = ctx;

    return ESP_OK;
}

voice_chat_state_t voice_chat_get_state(voice_chat_handle_t handle)
{
    if (!handle) {
        return VOICE_CHAT_STATE_ERROR;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;
    return ctx->state;
}

esp_err_t voice_chat_trigger(voice_chat_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;

    if (!ctx->is_running || ctx->config.mode != VOICE_CHAT_MODE_BUTTON) {
        return ESP_ERR_INVALID_STATE;
    }

    // Cancel any ongoing conversation and start new one
    esp_coze_chat_send_audio_cancel(ctx->coze_handle);
    voice_chat_set_state(ctx, VOICE_CHAT_STATE_LISTENING);

    ESP_LOGI(TAG, "Voice chat triggered manually");
    return ESP_OK;
}

esp_err_t voice_chat_cancel(voice_chat_handle_t handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_context_t *ctx = (voice_chat_context_t *)handle;

    if (!ctx->is_running) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = esp_coze_chat_send_audio_cancel(ctx->coze_handle);
    if (ret == ESP_OK) {
        voice_chat_set_state(ctx, VOICE_CHAT_STATE_CONNECTED);
    }

    return ret;
}

// Internal helper functions
static esp_err_t voice_chat_set_state(voice_chat_context_t *ctx, voice_chat_state_t new_state)
{
    if (!ctx) {
        return ESP_ERR_INVALID_ARG;
    }

    voice_chat_state_t old_state = ctx->state;
    ctx->state = new_state;

    ESP_LOGI(TAG, "State changed: %d -> %d", old_state, new_state);

    // Notify callback if set
    if (ctx->event_callback) {
        ctx->event_callback(new_state, NULL, ctx->event_ctx);
    }

    return ESP_OK;
}

static void voice_chat_audio_callback(char *data, int len, void *ctx)
{
    if (!ctx || !data || len <= 0) {
        return;
    }

    voice_chat_context_t *voice_ctx = (voice_chat_context_t *)ctx;

    // Play audio through PCM5101 audio driver
    // Note: This assumes PCM5101 has a function to play raw audio data
    // You may need to adapt this based on your actual audio driver implementation
    ESP_LOGI(TAG, "Received audio data: %d bytes", len);

    // Set audio ready event
    xEventGroupSetBits(voice_ctx->event_group, VOICE_CHAT_EVENT_AUDIO_READY);

    // TODO: Integrate with PCM5101 audio playback
    // This would depend on your specific audio driver implementation
    // For now, we'll just log the received data
}

static void voice_chat_event_handler(esp_coze_chat_event_t event, char *event_data, void *ctx)
{
    if (!ctx) {
        return;
    }

    voice_chat_context_t *voice_ctx = (voice_chat_context_t *)ctx;

    switch (event) {
        case ESP_COZE_CHAT_EVENT_CHAT_CREATE:
            ESP_LOGI(TAG, "Chat session created");
            voice_chat_set_state(voice_ctx, VOICE_CHAT_STATE_CONNECTED);
            break;

        case ESP_COZE_CHAT_EVENT_CHAT_SPEECH_STARTED:
            ESP_LOGI(TAG, "Speech started");
            voice_chat_set_state(voice_ctx, VOICE_CHAT_STATE_SPEAKING);
            xEventGroupSetBits(voice_ctx->event_group, VOICE_CHAT_EVENT_SPEECH_START);
            break;

        case ESP_COZE_CHAT_EVENT_CHAT_SPEECH_STOPED:
            ESP_LOGI(TAG, "Speech stopped");
            voice_chat_set_state(voice_ctx, VOICE_CHAT_STATE_CONNECTED);
            xEventGroupSetBits(voice_ctx->event_group, VOICE_CHAT_EVENT_SPEECH_STOP);
            break;

        case ESP_COZE_CHAT_EVENT_CHAT_COMPLETED:
            ESP_LOGI(TAG, "Chat completed");
            voice_chat_set_state(voice_ctx, VOICE_CHAT_STATE_CONNECTED);
            break;

        case ESP_COZE_CHAT_EVENT_CHAT_SUBTITLE_EVENT:
            if (event_data && voice_ctx->event_callback) {
                voice_ctx->event_callback(voice_ctx->state, event_data, voice_ctx->event_ctx);
            }
            ESP_LOGI(TAG, "Subtitle: %s", event_data ? event_data : "NULL");
            break;

        case ESP_COZE_CHAT_EVENT_CHAT_ERROR:
            ESP_LOGE(TAG, "Chat error: %s", event_data ? event_data : "Unknown error");
            voice_chat_set_state(voice_ctx, VOICE_CHAT_STATE_ERROR);
            break;

        default:
            ESP_LOGD(TAG, "Unhandled event: %d", event);
            break;
    }
}

static void voice_chat_audio_task(void *param)
{
    voice_chat_context_t *ctx = (voice_chat_context_t *)param;
    audio_data_t audio_data;

    ESP_LOGI(TAG, "Audio task started");

    while (true) {
        // Wait for audio data
        if (xQueueReceive(ctx->audio_queue, &audio_data, portMAX_DELAY) == pdTRUE) {
            if (audio_data.data && audio_data.len > 0) {
                // Send audio data to Coze
                esp_err_t ret = esp_coze_chat_send_audio_data(ctx->coze_handle,
                                                            (char *)audio_data.data,
                                                            audio_data.len);
                if (ret != ESP_OK) {
                    ESP_LOGW(TAG, "Failed to send audio data: %s", esp_err_to_name(ret));
                }

                // Free the audio data
                free(audio_data.data);
            }
        }

        // Check if we should exit
        if (!ctx->is_running) {
            break;
        }
    }

    ESP_LOGI(TAG, "Audio task ended");
    vTaskDelete(NULL);
}
