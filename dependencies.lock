dependencies:
  chmorgan/esp-audio-player:
    dependencies: []
    source:
      path: E:\APPprj\LinkPet\linkpet-s3\components\chmorgan__esp-audio-player
      type: local
    version: 1.0.7
  chmorgan/esp-libhelix-mp3:
    dependencies: []
    source:
      path: E:\APPprj\LinkPet\linkpet-s3\components\chmorgan__esp-libhelix-mp3
      type: local
    version: 1.0.3
  espressif/esp-dsp:
    dependencies: []
    source:
      path: E:\APPprj\LinkPet\linkpet-s3\components\espressif__esp-dsp
      type: local
    version: 1.4.12
  espressif/esp-sr:
    dependencies: []
    source:
      path: E:\APPprj\LinkPet\linkpet-s3\components\espressif__esp-sr
      type: local
    version: 1.9.4
  espressif/esp_websocket_client:
    component_hash: ac62982fcf9b266409c2299d2b6b1844122105b35163a3b7f8d0adaa9f7eb989
    dependencies:
    - name: idf
      require: private
      version: '>=5.0'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.5.0
  idf:
    source:
      type: idf
    version: 5.4.2
  lvgl/lvgl:
    dependencies: []
    source:
      path: E:\APPprj\LinkPet\linkpet-s3\components\lvgl__lvgl
      type: local
    version: 8.3.11
direct_dependencies:
- chmorgan/esp-audio-player
- chmorgan/esp-libhelix-mp3
- espressif/esp-dsp
- espressif/esp-sr
- espressif/esp_websocket_client
- idf
- lvgl/lvgl
manifest_hash: 0942caec33fcf4407816480883bedec3e86e7b75f78def5163819690c0f6ee4e
target: esp32s3
version: 2.0.0
