#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASH_MODE_AUTO_DETECT=y
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR=y
CONFIG_ESPTOOLPY_FLASHMODE="dio"
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHFREQ="80m"
CONFIG_ESPTOOLPY_FLASHSIZE_8MB=y
CONFIG_ESPTOOLPY_FLASHSIZE="8MB"
# end of Serial flasher config

#
# Compiler options
#
CONFIG_COMPILER_OPTIMIZATION_PERF=y
# end of Compiler options

#
# FAT Filesystem support
#
CONFIG_FATFS_LFN_HEAP=y
# end of FAT Filesystem support

#
# ESP-TLS
#
CONFIG_ESP_TLS_INSECURE=y
CONFIG_ESP_TLS_SKIP_SERVER_CERT_VERIFY=y
# end of ESP-TLS

#
# Audio Codec Device Configuration
#
# CONFIG_CODEC_I2C_BACKWARD_COMPATIBLE is not set
# end of Audio Codec Device Configuration

#
# FREERTOS
#
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_USE_TRACE_FACILITY=y
CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS=y
# CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES is not set
CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
# end of FREERTOS

#
# ESP Audio Simple Player
#
CONFIG_ESP_AUDIO_SIMPLE_PLAYER_FILE_EN=y
CONFIG_ESP_AUDIO_SIMPLE_PLAYER_RESAMPLE_EN=y
CONFIG_AUDIO_SIMPLE_PLAYER_RESAMPLE_DEST_RATE=16000
CONFIG_ESP_AUDIO_SIMPLE_PLAYER_CH_CVT_EN=y
CONFIG_AUDIO_SIMPLE_PLAYER_CH_CVT_DEST=2
CONFIG_ESP_AUDIO_SIMPLE_PLAYER_BIT_CVT_EN=y
CONFIG_AUDIO_SIMPLE_PLAYER_BIT_CVT_DEST_32BIT=y
# end of ESP Audio Simple Player