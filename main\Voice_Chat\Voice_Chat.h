#pragma once

#include "esp_err.h"
#include "esp_log.h"
#include "esp_coze_chat.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"

#ifdef __cplusplus
extern "C" {
#endif

// Voice chat events
#define VOICE_CHAT_EVENT_CONNECTED      (1 << 0)
#define VOICE_CHAT_EVENT_DISCONNECTED   (1 << 1)
#define VOICE_CHAT_EVENT_SPEECH_START   (1 << 2)
#define VOICE_CHAT_EVENT_SPEECH_STOP    (1 << 3)
#define VOICE_CHAT_EVENT_AUDIO_READY    (1 << 4)

// Voice chat modes
typedef enum {
    VOICE_CHAT_MODE_CONTINUOUS = 0,     // Continuous conversation mode
    VOICE_CHAT_MODE_WAKEWORD,           // Wake word triggered mode
    VOICE_CHAT_MODE_BUTTON,             // Button triggered mode
} voice_chat_mode_t;

// Voice chat state
typedef enum {
    VOICE_CHAT_STATE_IDLE = 0,
    VOICE_CHAT_STATE_CONNECTING,
    VOICE_CHAT_STATE_CONNECTED,
    VOICE_CHAT_STATE_LISTENING,
    VOICE_CHAT_STATE_SPEAKING,
    VOICE_CHAT_STATE_ERROR,
} voice_chat_state_t;

// Voice chat configuration
typedef struct {
    char *bot_id;                       // Coze bot ID
    char *access_token;                 // Coze access token
    char *user_id;                      // User ID
    char *voice_id;                     // Voice ID for TTS
    voice_chat_mode_t mode;             // Chat mode
    bool enable_subtitle;               // Enable subtitle display
    uint32_t audio_buffer_size;         // Audio buffer size
    uint8_t task_priority;              // Task priority
    uint8_t task_core;                  // Task core affinity
} voice_chat_config_t;

// Voice chat handle
typedef void* voice_chat_handle_t;

// Voice chat event callback
typedef void (*voice_chat_event_callback_t)(voice_chat_state_t state, const char *data, void *ctx);

// Default configuration
#define VOICE_CHAT_DEFAULT_CONFIG() {           \
    .bot_id = NULL,                             \
    .access_token = NULL,                       \
    .user_id = "linkpet_user",                  \
    .voice_id = "7426720361733144585",          \
    .mode = VOICE_CHAT_MODE_WAKEWORD,           \
    .enable_subtitle = true,                    \
    .audio_buffer_size = 4096,                  \
    .task_priority = 10,                        \
    .task_core = 1,                             \
}

/**
 * @brief Initialize voice chat module
 * 
 * @param config Voice chat configuration
 * @param handle Pointer to store the voice chat handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_init(const voice_chat_config_t *config, voice_chat_handle_t *handle);

/**
 * @brief Deinitialize voice chat module
 * 
 * @param handle Voice chat handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_deinit(voice_chat_handle_t handle);

/**
 * @brief Start voice chat session
 * 
 * @param handle Voice chat handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_start(voice_chat_handle_t handle);

/**
 * @brief Stop voice chat session
 * 
 * @param handle Voice chat handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_stop(voice_chat_handle_t handle);

/**
 * @brief Send audio data to voice chat
 * 
 * @param handle Voice chat handle
 * @param data Audio data buffer
 * @param len Audio data length
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_send_audio(voice_chat_handle_t handle, const uint8_t *data, size_t len);

/**
 * @brief Set event callback for voice chat
 * 
 * @param handle Voice chat handle
 * @param callback Event callback function
 * @param ctx User context
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_set_event_callback(voice_chat_handle_t handle, 
                                       voice_chat_event_callback_t callback, 
                                       void *ctx);

/**
 * @brief Get current voice chat state
 * 
 * @param handle Voice chat handle
 * @return voice_chat_state_t Current state
 */
voice_chat_state_t voice_chat_get_state(voice_chat_handle_t handle);

/**
 * @brief Trigger voice chat manually (for button mode)
 * 
 * @param handle Voice chat handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_trigger(voice_chat_handle_t handle);

/**
 * @brief Cancel current voice chat session
 * 
 * @param handle Voice chat handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t voice_chat_cancel(voice_chat_handle_t handle);

#ifdef __cplusplus
}
#endif
