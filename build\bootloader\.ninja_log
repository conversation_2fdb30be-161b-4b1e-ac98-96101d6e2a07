# ninja log v6
31	673	7762533559620965	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	5663f523c5eac221
81	709	7762533560122261	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	3ec43497e2624020
2308	2737	7762533582396819	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	7cfe7f0cf2c95bfc
2467	2821	7762533583982505	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	a105ea41b1e03ec6
173	951	7762533561048540	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	e9d33974fbea27ba
68	695	7762533559992323	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	c41db92f2defe22
93	829	7762533560235072	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	b4daca032bfcc456
161	881	7762533560919514	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	ab6e91df333391b6
3250	3539	7762533591818201	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	56f027c7529e3970
657	1287	7762533565884653	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	e66331a27c6849b
55	863	7762533559862229	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	ca48077aa6981fa9
590	1028	7762533565215541	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	13fa46f809f01b82
410	1214	7762533563414510	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	cf2b3b2e5e1ce729
310	1095	7762533562404166	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	590070b6480a5cc3
2384	2706	7762533583146682	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	b1fd5589e0282922
479	1140	7762533564106492	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	92725bcf1d614079
615	1169	7762533565461807	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	42459afb6a44ef96
578	1197	7762533565089009	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	630126497ffc83e9
696	1231	7762533566268845	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	c52c732d94b06e56
680	1253	7762533566117258	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	dd4327365f1a21a3
1956	2692	7762533578867176	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	730c3dfb15e82ab5
2751	3058	7762533586823100	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	1e9ce36a09e1f888
2267	2751	7762533581981287	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	5d4a63960f954a51
2239	2768	7762533581695819	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	cd76682d20f70185
244	1300	7762533561754277	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a775692b301c700e
602	1315	7762533565334207	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	f7904a436c5e232e
632	1328	7762533565629146	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	654cecf6b19fca05
1501	2120	7762533574323363	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	a16d14843bfb97fc
645	1354	7762533565761676	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	41f4dede7ca304dd
2902	3277	7762533588330996	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	8893ee0a3ccf602d
829	1501	7762533567595667	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	d67cda967511b80a
1924	2396	7762533578551650	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	b99ac37be794ec4a
952	1985	7762533568826192	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	e7035718637209b7
864	1653	7762533567950437	esp-idf/log/liblog.a	4f59662648024f
1095	1700	7762533570275944	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	1d8fbb4d6c36f4ab
1301	2308	7762533572321957	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ff3b18b93b88e02d
1329	2238	7762533572591883	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	c950ef12b289426d
1169	1841	7762533571002404	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	553a3638254546b5
1214	1898	7762533571447242	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	122818e1b51ced0d
1197	1884	7762533571277273	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	7c3b4faad7e22cad
1898	2467	7762533578285263	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	41d9f7cb663fdff3
1141	1910	7762533570725707	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	35b1d342fe007bd3
882	1923	7762533568130215	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	2f3bd9fbcf63bee4
1910	2384	7762533578415271	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	6832883829e1e262
1253	1943	7762533571842691	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	78f02d96979df35c
2849	3195	7762533587803118	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	5bec868e4ee66797
1231	1955	7762533571617753	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	cfab8a87f0a8aa1d
1028	1969	7762533569598305	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	eb96a1e63b85d92b
1288	1999	7762533572181855	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	89d69433d30209a1
2105	2835	7762533580362282	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	25a52b5f57c666b9
2120	2562	7762533580512254	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	289ef63fdaa5780b
710	2020	7762533566402705	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	dad706b2346800
1315	2104	7762533572461930	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	20758330e49d2b65
1700	2184	7762533576308978	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	a581f12e7fceec9a
1943	2890	7762533578737138	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	51a37ef422eb6f3
1653	2197	7762533575842349	esp-idf/esp_rom/libesp_rom.a	44bad42d01d21cfe
1354	2267	7762533572856141	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	263a9df534a3d297
2397	2902	7762533583276659	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	86854a339f5bf1f2
2562	3039	7762533584923135	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	b25db1a042845077
1884	2329	7762533578155253	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	5ad8aabbe7354229
5357	5705	7762533616311800	E:/APPprj/LinkPet/linkpet-s3/build/bootloader/.bin_timestamp	5a093f8d59cf5f0f
2197	2591	7762533581275803	esp-idf/esp_common/libesp_common.a	5c7735f1efd95188
1841	2342	7762533577717966	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	544300b5912c9d22
1999	2415	7762533579307119	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	f3af86129b539263
1969	2678	7762533578997213	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	fb0492b65c1ad241
2184	2724	7762533581155793	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	27268cb79e5fd548
2342	2849	7762533582726642	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	e106b2f9eb7ce734
2415	2875	7762533583456673	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	787ea969128deb48
2737	3071	7762533586673102	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	628514cdb9916198
2768	3087	7762533586993151	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	38c6dbda5cb920f4
1985	3103	7762533579157183	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	82b8da69d37b14e9
2591	3114	7762533585223186	esp-idf/esp_hw_support/libesp_hw_support.a	b5ae0add9467df36
2822	3140	7762533587523118	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	bfe310a115738a38
2020	3153	7762533579512995	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	889cc5796ad20b1b
2890	3168	7762533588213238	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	7cbec0b1683fba66
2679	3183	7762533586093151	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	3c872eccd4263e7a
2835	3210	7762533587663087	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f386e93634fb68bc
2329	3223	7762533582596711	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	ceeb2c8c3ed6d35d
3058	3386	7762533589888521	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	1e82dfafa2cf0484
2875	3235	7762533588063085	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	48c76db7493e3ae3
2692	3250	7762533586233159	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	949783ed8bc90181
3386	3593	7762533593174123	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	efdca77879039f4
2707	3317	7762533586373115	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	3f254806b9054bf9
3039	3369	7762533589698473	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	d0b31cd731f6629f
3088	3411	7762533590188011	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	d474f87243c3689d
3103	3440	7762533590337940	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	67479f16c10408ad
3183	3452	7762533591147980	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	de54813768c1ff9f
3141	3466	7762533590717999	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	2fd55360d4d038be
3071	3467	7762533590018498	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	ced729f2fe6dcfd5
3195	3475	7762533591267927	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	e6f2c374c1f64ee6
3114	3519	7762533590448032	esp-idf/esp_system/libesp_system.a	e8f758ef8ae8ea7c
3168	3496	7762533590997997	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	978ecfec15cc49d6
3236	3504	7762533591668151	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	47ef87ca829ed77a
3154	3513	7762533590848022	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d0b2efc62659495e
3210	3529	7762533591408081	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	4547ed71e4b603c6
3223	3532	7762533591537883	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	fd0bc714f6eff7f6
3278	3541	7762533592090793	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	3662db9cde6dbbd
3317	3558	7762533592477311	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	8f1763c8f304768c
3440	3611	7762533595338256	project_elf_src_esp32s3.c	c745eadbda7446d1
3440	3611	7762533595338256	E:/APPprj/LinkPet/linkpet-s3/build/bootloader/project_elf_src_esp32s3.c	c745eadbda7446d1
3370	3623	7762533593004168	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	9a490fdf2da9ce40
3411	3671	7762533593425824	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	ef1eeecec28d8bdf
3452	3730	7762533593825773	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	507df0898dd5c72a
3611	3750	7762533595420309	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	f328dce8cf27ab3e
3519	3790	7762533594498881	esp-idf/efuse/libefuse.a	6181e6d11d17d22d
3790	4041	7762533597207117	esp-idf/bootloader_support/libbootloader_support.a	8811b6ffeac93156
2724	4090	7762533586553166	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	bb1f416e90d5e171
4041	4192	7762533599723370	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	5571b23cca3e5cc2
4192	4330	7762533601237337	esp-idf/spi_flash/libspi_flash.a	8c40e6bb336d58b7
4330	4483	7762533602609872	esp-idf/hal/libhal.a	169e55f016280528
4483	4625	7762533604138779	esp-idf/micro-ecc/libmicro-ecc.a	3526c49196434cf4
4625	4817	7762533605561099	esp-idf/soc/libsoc.a	331007e7ca4a8b6f
4817	4960	7762533607474085	esp-idf/xtensa/libxtensa.a	598e044bb801c761
4960	5099	7762533608907244	esp-idf/main/libmain.a	f5f21f90e26addaf
5099	5357	7762533610304983	bootloader.elf	8eff0396ee0b5b13
5357	5705	7762533616311800	.bin_timestamp	5a093f8d59cf5f0f
5705	5806	7762533616361765	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
5705	5806	7762533616361765	E:/APPprj/LinkPet/linkpet-s3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
11	29386	7762534725072600	build.ninja	4eefde6751948bb4
35	306	7762534726534598	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
35	306	7762534726534598	E:/APPprj/LinkPet/linkpet-s3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
17	159	7762537469761692	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
17	159	7762537469761692	E:/APPprj/LinkPet/linkpet-s3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
17	117	7762538515004022	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
17	117	7762538515004022	E:/APPprj/LinkPet/linkpet-s3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c699ea2c3a0cc24d
